import { Expand<PERSON><PERSON>, MoreH<PERSON>z } from '@mui/icons-material'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Grid,
  IconButton,
  ListItem,
  Menu,
  Skeleton,
  TextField,
  Typography,
} from '@mui/material'
import { makeStyles } from '@mui/styles'
import Image from 'next/image'
import { useEffect, useRef, useState } from 'react'
import { Controller } from 'react-hook-form'

import AppAudioLibraryoPlayer from '../common/app-audio-play-library'
import VisuallyHiddenInput from '../common/hidden-input'
import ImageCropDialog from '../dialogs/image-crop-dialog'
import { AppDropzone } from '@memberup/shared/src/components/common/app-dropzone'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import useUploadFiles from '@/memberup/components/hooks/use-upload-files'
import SVGCloseSmall from '@/memberup/components/svgs/close-small'
import SVGEditNew from '@/memberup/components/svgs/edit-new'
import SVGPhotoAdd from '@/memberup/components/svgs/photo-add'
import SVGPhoto from '@/shared-components/svgs/photo'

const useStyles = makeStyles((theme) => ({
  root: {
    '& .MuiDialog-paper': {
      borderRadius: 8,
      maxWidth: 560,
      overflow: 'visible',
    },
    '& .MuiDialog-paperFullWidth': {
      width: '100%',
      height: '100%',
      maxHeight: '100%',
      maxWidth: '100%',
      margin: 0,
    },
    '& .MuiDivider-root': {
      borderColor: theme.palette.action.disabledBackground,
    },
    '& .MuiOutlinedInput-root': {
      backgroundColor: theme.palette.action.disabledBackground,
      borderRadius: 12,
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none',
      },
    },
  },
  buttons: {
    backgroundColor: 'transparent',
    borderRadius: 20,
    color: theme.palette.text.primary,
    borderColor: theme.palette.action.disabledBackground,
    borderStyle: 'solid',
    borderWidth: '1px',
    '&:hover': {
      backgroundColor: theme.palette.action.disabledBackground,
    },
  },
  mediaButtons: {
    borderRadius: '20px',
    backgroundColor: 'transparent',
    boxShadow: 'none',
    border: 'solid 1px',
    color: theme.palette.text.disabled,
    borderColor: theme.palette.action.disabledBackground,
    height: '56px',
    width: '200px',
    '&:hover': {
      backgroundColor: theme.palette.action.disabledBackground,
    },
  },
  mediaButtonIcon: {
    backgroundColor: theme.palette.action.disabledBackground,
    borderRadius: '50%',
    width: '32px',
    height: '32px',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
  },
  dialogTitle: {
    borderBottom: 'none',
    padding: 24,
  },
  dialogContent: {
    paddingLeft: 24,
    paddingRight: 24,
    paddingTop: 24,
    paddingBottom: 24,
  },
  inputFields: {
    height: 48,
    '& .MuiInputBase-input': {
      boxSizing: 'border-box',
      height: '100%',
      fontFamily: 'Graphik Regular',
      fontSize: '14px',
    },
  },
  textEditorWrapper: {
    borderColor: theme.palette.text.disabled,
    borderRadius: 12,
    borderWidth: '1px',
    marginTop: '48px',
    height: '200px',
    maxHeight: '400px',
    '&:focus': {
      borderColor: theme.palette.text.primary,
    },
  },
  audioPlayerWrapper: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    height: '100%',
    zIndex: 1,
  },
  inputLabel: {
    fontSize: '14px',
    fontFamily: 'Graphik Medium',
    lineHeight: '16px',
    color: theme.palette.mode == 'dark' ? 'rgba(255, 255, 255, 0.87)' : 'rgba(0, 0, 0, 0.87)',
  },
  subLabel: {
    color: '#8D94A3',
    fontSize: '12px',
    fontFamily: 'Graphik Regular',
    fontWeight: '500',
  },
}))

const ShowAudioMedia = ({
  uploadStatus,
  lessonFile,
  control,
  watch,
  selectedLessonId,
  setValue,
  setHasUnsavedChanges,
  setIsAudioLoaded,
  sections,
  selectedSectionId,
  setSections,
}) => {
  const [menuAnchorEl, setMenuAnchorEl] = useState(null)
  const [menuAnchorEl2, setMenuAnchorEl2] = useState(null)
  const membership = useStore((state) => state.community.membership)
  const { uploadProgress, uploadedFiles, initUploadFiles, handleUploadFiles } = useUploadFiles('library', membership.id)
  const [openImageCropper, setOpenImageCropper] = useState(false)
  const [thumbnailFile, setThumbnailFile] = useState(null)
  const [previousThumbnailFile, setPreviousThumbnailFile] = useState(null)
  const fileInputRef = useRef(null)

  useEffect(() => {
    if (uploadProgress === 100) {
      uploadedFiles[0]
      if (uploadedFiles) {
        setValue('thumbnail_url', uploadedFiles[0]?.url)
      }
    }
  }, [uploadedFiles, uploadProgress])
  const handleDropThumbnail = async (f) => {
    setPreviousThumbnailFile(thumbnailFile)
    // Upload the file to Cloudinary
    await initUploadFiles()
    const uploadedFiles = await handleUploadFiles([f], 'Cloudinary')
    // Set the URL of the uploaded file to the thumbnailFile state
    setThumbnailFile(uploadedFiles[0]?.url)
    // Open the ImageCropDialog
    setOpenImageCropper(true)
  }

  const handleRemoveThumbnail = () => {
    setValue('thumbnail_url', null)
    setHasUnsavedChanges(true)
    setThumbnailFile(null)
    setSections((prevSections) => {
      const updatedSections = { ...prevSections }
      updatedSections[selectedSectionId].ContentLibraryCourseLesson[selectedLessonId].thumbnail_url = null
      return updatedSections
    })
    handleMenuClose()
  }

  const classes = useStyles()

  const handleMenuClose = () => {
    setMenuAnchorEl(null)
  }
  const handleMenuClose2 = () => {
    setMenuAnchorEl2(null)
  }
  const handleMenuClick = (event) => {
    setMenuAnchorEl(event.currentTarget)
  }
  const handleMenuClick2 = (event) => {
    setMenuAnchorEl2(event.currentTarget)
  }

  const subtitleValue = watch('media_file_subtitle')
  const titleValue = watch('media_file_title')

  const handleDownloadAudio = async () => {
    try {
      const controller = new AbortController()
      const signal = controller.signal

      const response = await fetch(lessonFile?.url, { signal })

      const contentLength = response.headers.get('Content-Length')
      let isLengthComputable = !!contentLength
      let total = isLengthComputable ? parseInt(contentLength, 10) : 0
      let loaded = 0

      toast.info('Starting download...')

      if (isLengthComputable && response.body) {
        const reader = response.body.getReader()
        let chunks = [] // Array to hold chunks of data
        let reading = true
        while (reading) {
          const { done, value } = await reader.read()
          if (done) {
            reading = false
          } else {
            loaded += value.byteLength
            chunks.push(value)
            // Progress updates are not supported in this Sonner implementation
          }
        }

        // Combine all chunks into a single Blob
        const blob = new Blob(chunks, { type: 'audio/mpeg' }) // Adjust the MIME type if necessary
        const url = window.URL.createObjectURL(blob)
        downloadAudioFile(url, lessonFile)

        toast.success('Download complete!')
      } else {
        // Fallback to download without progress tracking
        const blob = await response.blob()
        const url = window.URL.createObjectURL(blob)
        downloadAudioFile(url, lessonFile)

        toast.success('Download complete!')
      }

      handleMenuClose()
    } catch (error) {
      if (error.name === 'AbortError') {
        toast.error('Download cancelled')
      } else {
        console.error('Fetch operation failed:', error)
        toast.error('Download failed')
      }
    }
  }

  const downloadAudioFile = (url, file) => {
    const link = document.createElement('a')
    link.href = url
    link.download = file?.filename || 'audiofile' // Use the filename or a default
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
  }

  const { theme } = useAppTheme()
  const isDarkTheme = useStore((state) => state.ui.isDarkTheme)

  return (
    <Box>
      {uploadStatus === 'processing' ? (
        <Skeleton
          variant="rectangular"
          height={'50vh'}
          sx={{
            marginBottom: '12px',
            borderRadius: '12px',
            width: '100%',
            height: '50vh',
          }}
        />
      ) : (
        <>
          <Box
            sx={{
              position: 'relative',
              width: '100%',
              height: '160px',
              overflow: 'hidden',
              borderRadius: '12px 12px 0px 0px',
              backgroundColor: isDarkTheme ? '#494c54' : '#ffffff',
            }}
          >
            <AppAudioLibraryoPlayer
              edit
              lesson={{
                thumbnail_url:
                  thumbnailFile?.croppedImg?.url ||
                  sections?.[selectedSectionId]?.ContentLibraryCourseLesson?.[selectedLessonId]?.thumbnail_url,
                media_file_title: watch('media_file_title'),
                media_file_subtitle: watch('media_file_subtitle'),
                media_file: watch('media_file'),
              }}
              lessonFile={lessonFile}
            />
          </Box>
          <Box
            sx={{
              '& .MuiAccordion-root': {
                borderRadius: '0px 0px 16px 16px !important',
              },
              '& .MuiAccordionSummary-root': {
                width: '185px',
              },
              position: 'relative',
            }}
          >
            <Accordion
              sx={{
                backgroundColor: isDarkTheme ? '#212124' : '#ffffff',
                padding: '16px 20px',
                boxShadow: isDarkTheme ? 'none' : '0px 5px 10px rgba(0, 0, 0, 0.1)',
              }}
            >
              <AccordionSummary
                expandIcon={<ExpandMore sx={{ maxWidth: '21px' }} />}
                aria-controls="panel1a-content"
                id="panel1a-header"
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                  }}
                >
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      backgroundColor: theme.palette.action.disabledBackground,
                      borderRadius: '12px',
                      width: '32px',
                      height: '32px',
                    }}
                  >
                    <SVGEditNew />
                  </Box>
                  <Typography
                    sx={{
                      ml: '8px',
                      fontFamily: 'Graphik Semibold',
                      fontSize: '14px',
                    }}
                  >
                    Customize media
                  </Typography>
                </Box>
              </AccordionSummary>

              <AccordionDetails>
                <Grid
                  container
                  spacing={4}
                  sx={{
                    mt: '24px',
                  }}
                >
                  <Grid item xs={4}>
                    <Typography
                      sx={{
                        fontFamily: 'Graphik Medium',
                        fontSize: '14px',
                      }}
                      gutterBottom
                    >
                      Audio Thumbnail
                    </Typography>
                    <Controller
                      name="thumbnail_url"
                      control={control}
                      render={({ field }) => {
                        if (field?.value) {
                          return (
                            <Box
                              sx={{
                                width: '190px',
                                height: '190px',
                                minHeight: '190px',
                                position: 'relative',
                              }}
                            >
                              <Button
                                onClick={handleMenuClick2}
                                variant="outlined"
                                sx={{
                                  zIndex: 2,
                                  position: 'absolute',
                                  bottom: '8px',
                                  right: '8px',
                                  margin: '0px',
                                  height: '32px',
                                  width: '32px',
                                  backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#F3F5F5',
                                  borderColor: isDarkTheme ? '#F3F5F5' : 'rgb(23, 23, 26)',
                                  borderRadius: 12,
                                  minHeight: '32px',
                                  minWidth: '32px',
                                  color: theme.palette.text.disabled,
                                  '&:hover': {
                                    backgroundColor: isDarkTheme ? '#29292c' : '#F3F5F5',
                                    borderColor: isDarkTheme ? '#F3F5F5' : '#29292c',
                                  },
                                }}
                              >
                                <MoreHoriz fontSize="small" />
                              </Button>
                              <Menu
                                anchorEl={menuAnchorEl2}
                                keepMounted
                                open={Boolean(menuAnchorEl2)}
                                onClose={handleMenuClose2}
                                sx={{
                                  '& .MuiPaper-root': {
                                    borderRadius: '12px',
                                    boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
                                    border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
                                    backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
                                    backgroundImage: 'unset',
                                    padding: '6px',
                                  },
                                  '& .MuiList-root': {
                                    padding: '0px',
                                  },
                                }}
                              >
                                <ListItem
                                  sx={{
                                    justifyContent: 'left',
                                    fontFamily: 'unset',
                                    height: '40px',
                                    margin: '0px',
                                    cursor: 'pointer',
                                    '&:hover': {
                                      backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
                                      borderRadius: '12px',
                                    },
                                  }}
                                  onClick={handleRemoveThumbnail}
                                >
                                  Remove Thumbnail
                                </ListItem>
                              </Menu>
                              <Button
                                component="label"
                                variant="outlined"
                                sx={{
                                  zIndex: 1,
                                  position: 'absolute',
                                  bottom: '8px',
                                  height: '32px',
                                  right: '44px',
                                  backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#F3F5F5',
                                  borderColor: isDarkTheme ? '#F3F5F5' : 'rgb(23, 23, 26)',
                                  borderRadius: 12,
                                  minHeight: '32px',
                                  '&:hover': {
                                    backgroundColor: isDarkTheme ? '#29292c' : '#F3F5F5',
                                    borderColor: isDarkTheme ? '#F3F5F5' : '#29292c',
                                  },
                                }}
                              >
                                <Typography
                                  sx={{
                                    fontSize: '13px',
                                    fontFamily: 'Graphik Medium',
                                    color: theme.palette.text.primary,
                                    lineHeight: '16px',
                                  }}
                                >
                                  <span
                                    style={{
                                      marginRight: '8px',
                                    }}
                                  >
                                    <SVGPhoto width={13} height={13} />
                                  </span>
                                  Replace
                                </Typography>
                                <VisuallyHiddenInput
                                  ref={fileInputRef}
                                  onChange={(e) => {
                                    //get file
                                    if (e.target.files) {
                                      handleDropThumbnail(e.target.files[0])
                                      setHasUnsavedChanges(true)
                                    }
                                    e.target.value = null
                                  }}
                                  type="file"
                                />
                              </Button>
                              {sections?.[selectedSectionId]?.ContentLibraryCourseLesson?.[selectedLessonId]
                                ?.thumbnail_url ? (
                                <Image
                                  src={
                                    thumbnailFile?.croppedImg?.url ||
                                    sections?.[selectedSectionId]?.ContentLibraryCourseLesson?.[selectedLessonId]
                                      ?.thumbnail_url
                                  }
                                  alt={'Audio Thumbnail'}
                                  layout="fill"
                                  objectFit="cover"
                                  style={{
                                    borderRadius: '12px',
                                  }}
                                />
                              ) : null}
                            </Box>
                          )
                        }

                        return (
                          <Box
                            className="background-color18 border-color02"
                            sx={{
                              display: 'flex',
                              flexDirection: 'column',
                              alignItems: 'center',
                              justifyContent: 'center',
                              height: '192px',
                              width: '192px',
                              borderRadius: '12px',
                              borderStyle: 'dashed',
                              borderWidth: '1px',
                              cursor: 'pointer',
                            }}
                          >
                            <AppDropzone
                              file={field?.value}
                              onDropFile={handleDropThumbnail}
                              placeholder={
                                <Box className="text-center">
                                  <Box
                                    className="flex justify-center"
                                    color={theme.palette.primary.main}
                                    marginBottom={'14px'}
                                  >
                                    <SVGPhotoAdd width={20} height={20} />
                                  </Box>
                                  <Typography
                                    sx={{
                                      fontSize: '14px',
                                      fontFamily: 'Graphik Medium',
                                      color: theme.palette.text.primary,
                                      lineHeight: '16px',
                                      marginBottom: '8px',
                                    }}
                                  >
                                    Add Thumbnail
                                  </Typography>
                                  <Typography
                                    sx={{
                                      fontSize: '12px',
                                      fontFamily: 'Graphik Regular',
                                      lineHeight: '16px',
                                      color: theme.palette.text.disabled,
                                    }}
                                  >
                                    Recommended size <br />
                                    288px x 288px
                                  </Typography>
                                </Box>
                              }
                            />
                          </Box>
                        )
                      }}
                    />
                  </Grid>
                  <Grid item xs={8}>
                    <Grid container spacing={2}>
                      <Grid item xs={12}>
                        <Box>
                          <Grid container justifyContent="space-between" alignItems="center">
                            <Grid item>
                              <Typography className={classes.inputLabel} gutterBottom>
                                Title
                              </Typography>
                            </Grid>
                            <Grid item>
                              <p className={classes.subLabel}>{50 - (titleValue?.length ? titleValue?.length : 0)}</p>
                            </Grid>
                          </Grid>
                          <Controller
                            name="media_file_title"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                              <TextField
                                {...field}
                                className={classes.inputFields}
                                placeholder="Title"
                                name="media_file_title"
                                variant="outlined"
                                fullWidth
                                InputProps={{
                                  inputProps: { maxLength: 50 },
                                }}
                              />
                            )}
                          />
                        </Box>
                      </Grid>
                      <Grid item xs={12}>
                        <Box>
                          <Grid container justifyContent="space-between" alignItems="center">
                            <Grid item>
                              <Typography className={classes.inputLabel} gutterBottom>
                                Subtitle
                              </Typography>
                            </Grid>
                            <Grid item>
                              <p className={classes.subLabel}>
                                {65 - (subtitleValue?.length ? subtitleValue?.length : 0)}
                              </p>
                            </Grid>
                          </Grid>
                          <Controller
                            name="media_file_subtitle"
                            control={control}
                            defaultValue=""
                            render={({ field }) => (
                              <TextField
                                {...field}
                                className={classes.inputFields}
                                name="media_file_subtitle"
                                placeholder="Subtitle"
                                variant="outlined"
                                multiline
                                InputProps={{
                                  inputProps: { maxLength: 65 },
                                }}
                                rows={4}
                                fullWidth
                              />
                            )}
                          />
                        </Box>
                      </Grid>
                    </Grid>
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
            <IconButton
              onClick={handleMenuClick}
              sx={{
                position: 'absolute',
                right: 9,
                top: 13,
                backgroundColor: theme.palette.action.disabledBackground,
              }}
            >
              <MoreHoriz />
            </IconButton>
          </Box>
          {openImageCropper && (
            <ImageCropDialog
              open={openImageCropper}
              onSaved={(image) => {
                setOpenImageCropper(false)
                // Set the entire image object to the thumbnailFile state
                setThumbnailFile(image)
                // Update the thumbnail_url of the selected lesson in the selected section
                setSections((prevSections) => {
                  const updatedSections = { ...prevSections }
                  updatedSections[selectedSectionId].ContentLibraryCourseLesson[selectedLessonId].thumbnail_url =
                    image.croppedImg.url
                  return updatedSections
                })
                setHasUnsavedChanges(true)
                setValue('thumbnail_url', image.croppedImg.url)
              }}
              onClose={() => setOpenImageCropper(false)}
              setImage={() => {}}
              image={thumbnailFile} // Pass the file to the dialog
              aspectRatio={560 / 560}
              initialCroppedArea={{
                croppedArea: thumbnailFile?.croppedImg?.croppedArea,
                croppedAreaPixels: thumbnailFile?.croppedImg?.croppedAreaPixels,
                zoom: thumbnailFile?.croppedImg?.zoom || thumbnailFile?.croppedImg?.zoom,
              }}
              onCancel={() => {
                setThumbnailFile(previousThumbnailFile)
                fileInputRef.current.value = null
                setOpenImageCropper(false)
                setValue('thumbnail_url', previousThumbnailFile?.croppedImg?.url)
              }}
              placeholderWidth={'560px'}
              placeholderHeight={'560px'}
              dialogCustomStyles={{ my: '-80px' }}
            />
          )}

          <Menu
            anchorEl={menuAnchorEl}
            keepMounted
            open={Boolean(menuAnchorEl)}
            onClose={handleMenuClose}
            sx={{
              '& .MuiPaper-root': {
                borderRadius: '12px',
                boxShadow: '1px 1px 5px 0 rgba(0, 0, 0, 0.2)',
                border: isDarkTheme ? 'solid 1px #2a2b30' : 'solid 1px #d7d9da',
                backgroundColor: isDarkTheme ? '#17171a' : '#ffffff',
                backgroundImage: 'unset',
                padding: '6px',
              },
              '& .MuiList-root': {
                padding: '0px',
              },
            }}
          >
            <ListItem
              sx={{
                justifyContent: 'left',
                fontFamily: 'Graphik Medium',
                fontSize: '13px',
                height: '40px',
                margin: '0px',
                cursor: 'pointer',
                '&:hover': {
                  backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
                  borderRadius: '12px',
                },
              }}
              onClick={handleDownloadAudio}
            >
              Download Audio
            </ListItem>
            <ListItem
              onClick={() => {
                setHasUnsavedChanges(true)
                setIsAudioLoaded(false)
                handleMenuClose()
                setValue('media_file', null)
              }}
              sx={{
                justifyContent: 'left',
                height: '40px',
                fontFamily: 'Graphik Medium',
                fontSize: '13px',
                margin: '0px',
                cursor: 'pointer',
                color: theme.palette.error.main,
                '&:hover': {
                  backgroundColor: isDarkTheme ? '#1b1b1f' : '#f8f8f8',
                  borderRadius: '12px',
                },
              }}
            >
              Remove Audio
            </ListItem>
          </Menu>
        </>
      )}
    </Box>
  )
}

export default ShowAudioMedia
