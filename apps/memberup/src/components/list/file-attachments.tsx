import { Box, IconButton, Typography } from '@mui/material'
import Image from 'next/image'

import useAppTheme from '../hooks/use-app-theme'
import SVGDownload from '../svgs/download'
import SVGDownloadClose from '../svgs/download-close'
import SVGEditNew from '../svgs/edit-new'
import SVGExternalLink from '../svgs/external-link'
import { shortenFileName } from '@memberup/shared/src/libs/string-utils'
import { toast } from '@/components/ui/sonner'
import { getFileTypeIcon } from '@/memberup/libs/utils'

const FileAttachments = ({
  files,
  onDelete,
  showDelete = true,
  showEdit = true,
  containerStyle = {},
  handleEditLink = undefined,
  handleEditResourceFile = undefined,
  showFilename = false,
  isModal = false,
  inputTitle = '',
}) => {
  const { theme, isDarkTheme, isMobile } = useAppTheme()
  const downloadFile = async (url, filename) => {
    const response = await fetch(url)

    const contentLength = response.headers.get('Content-Length')
    let isLengthComputable = !!contentLength
    let total = isLengthComputable ? parseInt(contentLength, 10) : 0
    let loaded = 0

    toast.info('Starting download...')

    if (isLengthComputable && response.body) {
      const reader = response.body.getReader()
      let chunks = [] // Array to hold chunks of data
      let reading = true
      while (reading) {
        const { done, value } = await reader.read()
        if (done) {
          reading = false
        } else {
          loaded += value.byteLength
          chunks.push(value)
          // Progress updates are not supported in this Sonner implementation
        }
      }

      // Combine all chunks into a single Blob
      const blob = new Blob(chunks)
      const blobUrl = URL.createObjectURL(blob)
      createAndClickLink(blobUrl, filename)

      toast.success('Download complete')
    } else {
      // Fallback to download without progress tracking
      const blob = await response.blob()
      const blobUrl = URL.createObjectURL(blob)
      createAndClickLink(blobUrl, filename)

      toast.success('Download complete')
    }
  }

  const createAndClickLink = (blobUrl, filename) => {
    const link = document.createElement('a')
    link.href = blobUrl
    link.download = filename
    link.style.display = 'none'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(blobUrl)
  }

  if (!files?.length) return null

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          flexWrap: 'wrap',
          gap: 2,
          justifyContent: { xs: 'center', sm: 'flex-start' },
        }}
      >
        {files.map((file, index) => {
          let { filename, mimetype, url } = file
          const iconSrc = mimetype ? getFileTypeIcon(mimetype, filename || file?.text || 'File') : 'other'
          const isLink = file?.type == 'link'
          let displayName =
            inputTitle ||
            (isLink
              ? filename?.length > 30
                ? filename?.substring(0, 30) + '...'
                : filename
              : showFilename
                ? shortenFileName(filename || file?.text, 23)
                : (file?.title && file?.title.length > 30 ? file?.title.substring(0, 30) + '...' : file?.title) ||
                  (file?.text || file?.filename || 'file').substring(0, 26) + '...')
          return (
            <Box
              key={index}
              sx={{
                width: isModal ? 452 : 344,
                height: isModal ? 48 : 56,
                borderRadius: '12px',
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                justifyContent: 'start',
                position: 'relative',
                backgroundColor: isDarkTheme ? '#2e2f34' : '#ebefef',
              }}
            >
              {showDelete && file ? (
                <IconButton
                  sx={{
                    position: 'absolute',
                    top: 'auto',
                    right: isModal ? '16px' : '4px',
                    color: theme.palette.text.disabled,
                  }}
                  onClick={() => onDelete(file)}
                >
                  <SVGDownloadClose />
                </IconButton>
              ) : null}
              {!isLink && !showDelete ? (
                <IconButton
                  sx={{
                    position: 'absolute',
                    top: 'auto',
                    right: '10px',
                    color: 'rgb(141, 148, 163)',
                  }}
                  onClick={() => downloadFile(url, filename)}
                >
                  <SVGDownload />
                </IconButton>
              ) : !showDelete && isLink ? (
                <IconButton
                  sx={{
                    position: 'absolute',
                    top: 'auto',
                    right: '10px',
                    color: 'rgb(141, 148, 163)',
                  }}
                  onClick={() => {
                    window.open(url, '_blank')
                  }}
                >
                  <SVGExternalLink />
                </IconButton>
              ) : null}
              {showEdit && (
                <IconButton
                  sx={{
                    position: 'absolute',
                    top: 'auto',
                    right: '35px',
                    color: 'rgb(141, 148, 163)',
                  }}
                  onClick={() =>
                    isLink
                      ? handleEditLink({
                          url: file.url,
                          text: file.filename,
                          public_id: file.public_id,
                        })
                      : handleEditResourceFile({
                          file,
                        })
                  }
                >
                  <SVGEditNew />
                </IconButton>
              )}

              <Box
                sx={{
                  backgroundColor: isDarkTheme ? 'rgba(141, 148, 163, 0.08)' : '#585D6608',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '40px',
                  height: '40px',
                  borderRadius: '12px',
                  marginLeft: '8px',
                }}
              >
                {isLink ? (
                  <Image
                    src={`/assets/default/images/icons/link.png`}
                    alt={filename}
                    style={{ maxWidth: '100%', maxHeight: '100%' }}
                    width={30}
                    height={24}
                  />
                ) : (
                  <Image
                    src={`/assets/default/images/icons/${iconSrc}`}
                    alt={filename}
                    style={{ maxWidth: '100%', maxHeight: '100%' }}
                    width={18}
                    height={24}
                  />
                )}
              </Box>
              <Box
                sx={{
                  marginLeft: '8px',
                }}
              >
                <Typography
                  style={{
                    wordBreak: 'break-all',
                    fontFamily: 'Graphik Medium',
                    fontSize: '14px',
                    lineHeight: '16px',
                    cursor: isLink ? 'pointer' : 'default',
                  }}
                  onClick={() => {
                    if (isLink) {
                      window.open(url, '_blank')
                    }
                  }}
                >
                  {displayName}
                </Typography>
              </Box>
            </Box>
          )
        })}
      </Box>
    </>
  )
}

export default FileAttachments
