'use client'

import { cn } from '@/lib/utils'

export enum ControlTypes {
  input = 'input',
  select = 'select',
  textarea = 'textarea',
}

export enum ControlVariants {
  default = 'default',
  transparent = 'transparent',
}

export function Control({
  children,
  className,
  disabled = false,
  error = false,
  placeholder,
  rightContent,
  active,
  focused,
  type,
  variant = ControlVariants.default,
  ...props
}: React.HTMLAttributes<HTMLDivElement> & {
  disabled?: boolean
  error?: boolean
  placeholder?: string
  rightContent?: React.ReactNode
  active?: boolean
  focused?: boolean
  type: ControlTypes
  variant?: ControlVariants
}) {
  const inputActive = active || focused

  let background =
    !inputActive &&
    (variant === ControlVariants.default ? 'bg-white-100 dark:bg-black-300' : 'dark:bg-black-100/[0.08]')

  if (inputActive && variant === ControlVariants.transparent) {
    background = 'bg-[#1d1b26]'
  }

  let borderColor = 'border-white-100 dark:border-black-300'

  if (error) {
    borderColor = 'border-red-200'
  } else if (focused) {
    borderColor = 'border-black-200 dark:border-black-100'
  } else if (variant === ControlVariants.transparent) {
    if (inputActive) {
      borderColor = 'dark:border-black-100/30'
    } else {
      borderColor = 'dark:border-transparent'
    }
  }

  const onLabelClick = (event: React.MouseEvent<HTMLLabelElement>) => {
    const inputOrTextarea = (event.target as HTMLElement).parentElement?.querySelector('input, textarea')

    if (inputOrTextarea) {
      ;(inputOrTextarea as HTMLInputElement).focus()
    }
  }

  return (
    <div
      className={cn(
        'control relative -mt-[0.5625rem] inline-block w-full pt-[0.5625rem]',
        variant === ControlVariants.transparent && 'variant-transparent',
        className,
      )}
      {...props}
    >
      {placeholder && (
        <label
          className={cn(
            'linear absolute left-3 top-0 origin-left translate-y-6 select-none px-1 text-sm transition-transform duration-200',
            disabled ? 'text-grey-400 dark:text-grey-700' : 'text-black-200 dark:text-black-100',
            inputActive && 'translate-y-0',
            type !== 'select' && 'cursor-text',
            type === 'select' ? 'z-10' : 'z-30',
            inputActive && variant === ControlVariants.transparent && 'bg-black-500/50',
          )}
          onClick={onLabelClick}
        >
          {placeholder}
        </label>
      )}
      <div className="input-container my-0.5 flex w-full overflow-hidden">
        {children}
        {rightContent && <div className="z-10 pb-1 pl-1 pr-[0.3125rem] pt-1">{rightContent}</div>}
      </div>
      <fieldset
        className={cn(
          'linear absolute top-0 z-0 h-full w-full rounded-base border px-[10px] transition-colors duration-200',
          background,
          borderColor,
        )}
      >
        {placeholder && (
          <legend
            className={cn(
              'invisible -top-[0.4375rem] h-[18px] max-w-[0.01px] select-none overflow-hidden text-nowrap text-sm',
              inputActive && 'max-w-full',
            )}
          >
            <span className="px-1">{placeholder}</span>
          </legend>
        )}
      </fieldset>
    </div>
  )
}
