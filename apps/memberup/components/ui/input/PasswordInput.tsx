'use client'

import React, { useState } from 'react'

import { Button } from '../button'
import { Input, InputProps } from './Input'
import { VisibilityOff20Icon, VisibilityOn20Icon } from '@/components/icons'
import { ControlVariants } from '@/components/ui/control/Control'
import { cn } from '@/lib/utils'

export interface PasswordInputProps extends InputProps {
  variant?: ControlVariants
}

const PasswordInput = React.forwardRef<HTMLInputElement, PasswordInputProps>((props, ref) => {
  const [showPassword, setShowPassword] = useState(false)
  const iconClassName = cn(
    'relative z-10 text-black-200 absolute left-0 top-0',
    props.disabled ? 'text-grey-500' : 'dark:text-black-100',
  )

  const onButtonClick = (event: any) => {
    event.preventDefault()

    if (props.disabled) return

    setShowPassword(!showPassword)
  }

  return (
    <Input
      append={
        <Button
          variant="inline"
          className={cn(
            'flex flex-col justify-center px-4',
            !props.disabled && '[&:hover_svg]:text-black-100 [&:hover_svg]:dark:text-black-200',
            props.disabled && 'cursor-not-allowed',
          )}
          onClick={onButtonClick}
        >
          <div className="relative h-5 w-5">
            <VisibilityOn20Icon className={cn(iconClassName, showPassword ? 'opacity-1' : 'opacity-0')} />
            <VisibilityOff20Icon className={cn(iconClassName, showPassword ? 'opacity-0' : 'opacity-1')} />
          </div>
        </Button>
      }
      {...props}
      ref={ref}
      type={showPassword ? 'text' : 'password'}
    />
  )
})

PasswordInput.displayName = 'PasswordInput'

export { PasswordInput }
