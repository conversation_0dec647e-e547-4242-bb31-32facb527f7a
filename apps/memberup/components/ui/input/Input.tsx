import { forwardRef, InputHTMLAttributes, ReactNode } from 'react'

import { AutofillDetector } from './AutofillDetector'
import { Control, ControlTypes, ControlVariants } from '@/components/ui/control/Control'
import { cn } from '@/lib/utils'

export interface InputProps extends InputHTMLAttributes<HTMLInputElement> {
  inputClassName?: string
  rightContent?: ReactNode
  error?: boolean
  prepend?: ReactNode
  append?: ReactNode
  variant?: ControlVariants
}

const Input = forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      error = false,
      disabled = false,
      inputClassName,
      placeholder,
      prepend,
      append,
      type,
      value,
      variant,
      onFocus,
      onBlur,
      rightContent,
      ...props
    },
    externalRef,
  ) => {
    return (
      <AutofillDetector
        value={value}
        externalRef={externalRef}
        render={({ active, isFocused, ref, hasAutofilled, setHasAutofilled, setIsFocused }) => (
          <Control
            active={active}
            className={className}
            disabled={disabled}
            rightContent={rightContent}
            error={error}
            focused={isFocused}
            placeholder={placeholder}
            type={ControlTypes.input}
            variant={variant}
          >
            {prepend}
            <input
              type={type}
              className={cn(
                'relative z-20 mx-4 flex h-11 w-full border-0 bg-transparent py-1 text-base file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none disabled:cursor-not-allowed sm:text-sm',
                prepend && 'pl-0',
                disabled ? 'text-grey-500 dark:text-grey-500' : 'text-black-700 dark:text-white-500',
                inputClassName,
                active && 'active',
              )}
              disabled={disabled}
              ref={ref}
              value={value}
              onBlur={(e) => {
                setIsFocused(false)
                if (hasAutofilled) {
                  setHasAutofilled(false)
                }
                onFocus && onFocus(e)
              }}
              onFocus={(e) => {
                setIsFocused(true)
                onBlur && onBlur(e)
              }}
              {...props}
            />
            {append}
          </Control>
        )}
      />
    )
  },
)
Input.displayName = 'Input'

export { Input }
