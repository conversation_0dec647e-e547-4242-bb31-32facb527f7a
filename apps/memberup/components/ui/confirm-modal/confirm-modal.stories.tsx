import { Meta, StoryObj } from '@storybook/react'
import { useState } from 'react'

import { Button } from '../button'
import { ConfirmModal } from './confirm-modal'

const meta: Meta<typeof ConfirmModal> = {
  title: 'ui/ConfirmModal',
  component: ConfirmModal,
  argTypes: {},
  parameters: {
    layout: 'centered',
  },
}
export default meta

type Story = StoryObj<typeof ConfirmModal>

const ConfirmModalWithState = () => {
  const [open, setOpen] = useState(false)

  return (
    <>
      <Button onClick={() => setOpen(true)}>Open modal</Button>
      <ConfirmModal
        title="Are you sure you want to remove this card?"
        onConfirm={() => setOpen(false)}
        open={open}
        onCancel={() => setOpen(false)}
      />
    </>
  )
}

export const Base: Story = {
  render: () => <ConfirmModalWithState />,
  args: {},
}
