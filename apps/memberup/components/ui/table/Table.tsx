import { cva, type VariantProps } from 'class-variance-authority'
import * as React from 'react'

import { cn } from '@/lib/utils'

const Table = React.forwardRef<HTMLTableElement, React.HTMLAttributes<HTMLTableElement>>(
  ({ className, ...props }, ref) => (
    <div className="relative w-full overflow-auto">
      <table ref={ref} className={cn('w-full caption-bottom text-sm', className)} {...props} />
    </div>
  ),
)
Table.displayName = 'Table'

const TableHeader = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(
  ({ className, ...props }, ref) => <thead ref={ref} className={cn('[&_tr]:border-b-2', className)} {...props} />,
)
TableHeader.displayName = 'TableHeader'

const TableBody = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(
  ({ className, ...props }, ref) => (
    <tbody
      ref={ref}
      className={cn(
        '[&_tr:hover_td]:md:after:bg-white-100 [&_tr:hover_td]:md:dark:after:bg-black-300 [&_tr_td]:md:after:absolute [&_tr_td]:md:after:left-0 [&_tr_td]:md:after:z-0 [&_tr_td]:md:after:block [&_tr_td]:md:after:w-full [&_tr_td]:md:after:transition-colors [&_tr_td]:md:after:content-[""]',
        className,
      )}
      {...props}
    />
  ),
)
TableBody.displayName = 'TableBody'

const TableFooter = React.forwardRef<HTMLTableSectionElement, React.HTMLAttributes<HTMLTableSectionElement>>(
  ({ className, ...props }, ref) => (
    <tfoot ref={ref} className={cn('border-t bg-muted/50 font-medium [&>tr]:last:border-b-0', className)} {...props} />
  ),
)
TableFooter.displayName = 'TableFooter'

const tableRowVariants = cva('', {
  variants: {
    variant: {
      default:
        'border-b-2 md:[&_td:after]:top-4 md:[&_td:after]:h-[calc(100%_-_2rem)] md:[&_td:first-child]:pl-6 md:[&_th:first-child]:pl-6 md:[&_td:last-child]:pr-6 md:[&_th:last-child]:pr-6 md:[&_td:first-child:after]:rounded-l-base md:[&_td:last-child:after]:rounded-r-base [&_td]:py-4 [&_th]:py-4 [&_td]:md:py-8 [&_th]:md:py-5',
      condensed:
        'border-b md:[&_td]:after:top-0 md:[&_td]:after:h-full md:[&_td]:after:w-full md:[&_td:first-child]:pl-4 md:[&_th:first-child]:pl-4',
      basic: '',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
  compoundVariants: [
    {
      variant: ['default', 'condensed'],
      class: 'border-b-grey-200 dark:border-b-grey-900 transition-colors relative data-[state=selected]:bg-muted',
    },
  ],
})

export interface TableRowProps
  extends React.HTMLAttributes<HTMLTableRowElement>,
    VariantProps<typeof tableRowVariants> {
  asChild?: boolean
  loading?: boolean
}

const TableRow = React.forwardRef<HTMLTableRowElement, TableRowProps>(({ className, variant, ...props }, ref) => (
  <tr ref={ref} className={cn(tableRowVariants({ className, variant }), className)} {...props} />
))
TableRow.displayName = 'TableRow'

const TableHead = React.forwardRef<HTMLTableCellElement, React.ThHTMLAttributes<HTMLTableCellElement>>(
  ({ className, ...props }, ref) => (
    <th
      ref={ref}
      className={cn(
        'h-10 px-2 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]',
        className,
      )}
      {...props}
    />
  ),
)
TableHead.displayName = 'TableHead'

const TableCell = React.forwardRef<HTMLTableCellElement, React.TdHTMLAttributes<HTMLTableCellElement>>(
  ({ className, children, ...props }, ref) => (
    <td
      ref={ref}
      className="relative z-10 align-middle [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]"
      {...props}
    >
      <div className={cn('relative z-10', className)}>{children}</div>
    </td>
  ),
)
TableCell.displayName = 'TableCell'

const TableCaption = React.forwardRef<HTMLTableCaptionElement, React.HTMLAttributes<HTMLTableCaptionElement>>(
  ({ className, ...props }, ref) => (
    <caption ref={ref} className={cn('mt-4 text-sm text-muted-foreground', className)} {...props} />
  ),
)
TableCaption.displayName = 'TableCaption'

export { Table, TableHeader, TableBody, TableFooter, TableHead, TableRow, TableCell, TableCaption }
