import { Meta, StoryObj } from '@storybook/react'

import { Skeleton } from './Skeleton'
import { SkeletonBox as SkeletonBoxComponent } from './SkeletonBox'
import { SkeletonCard as SkeletonCardComponent } from './SkeletonCard'
import { SkeletonPost as SkeletonPostComponent } from './SkeletonPost'

const meta: Meta<typeof Skeleton> = {
  title: 'ui/Skeleton',
  component: Skeleton,
  argTypes: {},
  parameters: {
    layout: 'centered',
  },
}
export default meta

type Story = StoryObj<typeof Skeleton>

export const SkeletonBox: Story = {
  render: () => (
    <div className="w-80 bg-white-400 p-10 dark:bg-grey-900">
      <SkeletonBoxComponent />
    </div>
  ),
  args: {},
}

export const SkeletonCard: Story = {
  render: () => (
    <div className="w-80 bg-white-400 p-10 dark:bg-grey-900">
      <SkeletonCardComponent />
    </div>
  ),
  args: {},
}

export const SkeletonPost: Story = {
  render: () => (
    <div className="bg-white-400 p-10 dark:bg-grey-900">
      <SkeletonPostComponent />
    </div>
  ),
  args: {},
}
