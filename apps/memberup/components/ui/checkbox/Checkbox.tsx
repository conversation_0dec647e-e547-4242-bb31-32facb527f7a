'use client'

import * as CheckboxPrimitive from '@radix-ui/react-checkbox'
import { cva, type VariantProps } from 'class-variance-authority'
import * as React from 'react'

import { Check16Icon } from '@/components/icons'
import { cn } from '@/lib/utils'

const checkboxVariants = cva(
  'data-[state=checked]:text-primary-foreground peer h-5 w-5 shrink-0 cursor-pointer rounded border focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default:
          'border-black-200 hover:border-primary-200 data-[state=checked]:border-primary-200 data-[state=checked]:bg-primary data-[state=checked]:hover:ring-1 data-[state=checked]:hover:ring-grey-200 dark:border-black-100 dark:hover:border-primary-200 dark:data-[state=checked]:border-primary-200 dark:data-[state=checked]:hover:ring-grey-800',
        'community-primary':
          'border-black-200 hover:border-community-primary data-[state=checked]:border-community-primary data-[state=checked]:bg-community-primary data-[state=checked]:hover:ring-1 data-[state=checked]:hover:ring-grey-200 dark:border-black-100 dark:hover:border-community-primary dark:data-[state=checked]:border-community-primary dark:data-[state=checked]:hover:ring-grey-800',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
)

export interface CheckboxProps
  extends React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>,
    VariantProps<typeof checkboxVariants> {}

const Checkbox = React.forwardRef<React.ElementRef<typeof CheckboxPrimitive.Root>, CheckboxProps>(
  ({ className, variant, checked, ...props }, ref) => {
    return (
      <div className="checkbox flex flex-col items-start">
        <CheckboxPrimitive.Root
          ref={ref}
          className={cn(checkboxVariants({ variant }), className)}
          checked={checked}
          {...props}
        >
          <CheckboxPrimitive.Indicator className={cn('flex items-center justify-center text-current')}>
            <Check16Icon className="h-4 w-4 text-white-500" />
          </CheckboxPrimitive.Indicator>
        </CheckboxPrimitive.Root>
      </div>
    )
  },
)
Checkbox.displayName = CheckboxPrimitive.Root.displayName

export { Checkbox }
