'use client'

import * as TooltipPrimitive from '@radix-ui/react-tooltip'
import * as React from 'react'

import { cn } from '@/lib/utils'

function TooltipProvider({ children, delayDuration, ...props }: { children: React.ReactNode; delayDuration?: number }) {
  const delay = delayDuration ?? 200

  return (
    <TooltipPrimitive.Provider delayDuration={delay} {...props}>
      {children}
    </TooltipPrimitive.Provider>
  )
}

const Tooltip = TooltipPrimitive.Root

const TooltipPortal = TooltipPrimitive.Portal

const TooltipTrigger = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Trigger>
>(({ asChild, className, ...props }, ref) => (
  <TooltipPrimitive.Trigger
    asChild
    ref={ref}
    className={cn(!asChild && 'bg-transparent', 'cursor-pointer', className)}
    {...props}
  />
))
TooltipTrigger.displayName = TooltipPrimitive.Trigger.displayName

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Content
    ref={ref}
    sideOffset={sideOffset}
    className={cn(
      'z-[1500] overflow-hidden rounded-xl border border-white-200 bg-white-400/90 px-3 py-[0.4375rem] text-[0.8125rem] text-grey-700 animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 dark:border-grey-800 dark:bg-grey-900/90 dark:text-grey-600',
      className,
    )}
    {...props}
  />
))
TooltipContent.displayName = TooltipPrimitive.Content.displayName

export { Tooltip, TooltipPortal, TooltipTrigger, TooltipContent, TooltipProvider }
