import { toast as sonnerToast } from 'sonner'

import { Check20Icon, Info20Icon, Report20Icon } from '@/components/icons'
import { cn } from '@/lib/utils'

const ToastContainer = ({ className, ...props }: any) => {
  return (
    <div
      className={cn(
        'toast-container group flex items-center rounded-base border border-grey-200 bg-white-500 p-4 text-left text-ssm font-semibold text-black-700 shadow-xl dark:border-grey-900 dark:bg-black-500 dark:text-white-500 md:min-w-80',
        className,
      )}
      {...props}
    />
  )
}
export const toast = {
  custom: (render: () => any, options?: any) => {
    sonnerToast.custom(render, options)
  },
  success: (message: React.ReactNode, options?: any) => {
    sonnerToast.custom(
      () => (
        <ToastContainer>
          <Check20Icon className="mr-2 shrink-0 text-green-200" />
          {message}
        </ToastContainer>
      ),
      options,
    )
  },
  error: (message: React.ReactNode, options?: any) => {
    sonnerToast.custom(
      () => (
        <ToastContainer>
          <Report20Icon className="mr-2 shrink-0 text-red-200" />
          {message}
        </ToastContainer>
      ),
      options,
    )
  },
  info: (message: React.ReactNode, options?: any) => {
    sonnerToast.custom(
      () => (
        <ToastContainer>
          <Info20Icon className="mr-2 shrink-0 text-orange-100" />
          {message}
        </ToastContainer>
      ),
      options,
    )
  },
}
