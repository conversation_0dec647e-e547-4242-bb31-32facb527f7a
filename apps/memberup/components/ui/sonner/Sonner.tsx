'use client'

import { useTheme } from 'next-themes'
import { Toaster as Son<PERSON> } from 'sonner'

import { Check20Icon, Info20Icon, Report20Icon } from '@/components/icons'

type ToasterProps = React.ComponentProps<typeof Sonner>

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = 'system' } = useTheme()

  return (
    <Sonner
      icons={{
        error: <Report20Icon />,
        info: <Info20Icon />,
        success: <Check20Icon />,
      }}
      offset="40px"
      position="top-right"
      theme={theme as ToasterProps['theme']}
      toastOptions={{
        className: 'tailwind-component p-0 bg-transparent border-transparent shadow-none flex justify-end',
      }}
      {...props}
    />
  )
}

export { Toaster }
