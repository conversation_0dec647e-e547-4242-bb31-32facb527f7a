import { LeftArrow24Icon } from '@/components/icons'
import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

export function DialogHeadingTitle({
  title,
  backAction,
  className,
}: {
  title: string
  backAction?: () => void
  className?: string
}) {
  return (
    <div
      className={cn(
        'align-center relative flex h-11 w-full justify-center bg-white-500 px-4 dark:bg-dark-background-3 md:bg-transparent',
        className,
      )}
    >
      {backAction && (
        <Button variant="inline" onClick={backAction} className="absolute left-0 top-0 h-full px-4 text-ui-dark-1000">
          <LeftArrow24Icon />
        </Button>
      )}
      <div className="flex items-center font-semibold dark:text-ui-dark-1000">
        <span>{title}</span>
      </div>
    </div>
  )
}
