'use client'

import * as SwitchPrimitives from '@radix-ui/react-switch'
import * as React from 'react'

import { cn } from '@/lib/utils'

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      'peer relative inline-flex h-6 w-12 shrink-0 cursor-pointer items-center rounded-full border-[0.1875rem] border-transparent bg-white-200 text-black-200 shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary-100 data-[state=checked]:text-white-200 dark:bg-grey-800 dark:text-black-200 data-[state=checked]:dark:bg-primary-100 dark:data-[state=checked]:text-white-200',
      className,
    )}
    {...props}
    ref={ref}
  >
    <span
      className="absolute left-0 top-1/2 w-[1.1875rem] -translate-y-1/2 text-right text-[0.5rem] font-semibold opacity-0 transition-opacity data-[state=checked]:opacity-100"
      data-state={props.checked ? 'checked' : 'unchecked'}
    >
      ON
    </span>
    <span
      className="absolute right-0 top-1/2 w-[1.1875rem] -translate-y-1/2 text-left text-[0.5rem] font-medium opacity-100 transition-opacity data-[state=checked]:opacity-0"
      data-state={props.checked ? 'checked' : 'unchecked'}
    >
      OFF
    </span>
    <SwitchPrimitives.Thumb
      className={cn(
        'pointer-events-none ml-0 block h-[1.125rem] w-[1.125rem] rounded-full bg-white-500 shadow-lg ring-0 transition-[margin] data-[state=checked]:ml-[1.5rem] data-[state=unchecked]:ml-0',
      )}
    />
  </SwitchPrimitives.Root>
))
Switch.displayName = SwitchPrimitives.Root.displayName

export { Switch }
