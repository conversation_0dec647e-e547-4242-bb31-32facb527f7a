import { DialogDescription } from '@radix-ui/react-dialog'
import React from 'react'
import { useMediaQuery } from 'react-responsive'

import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Drawer, DrawerContent, DrawerDescription, DrawerHeader, DrawerTitle } from '@/components/ui/drawer'
import { smMediaQuery } from '@/lib/client/media-queries'
import { cn } from '@/lib/utils'

function Modal({
  children,
  open,
  onOpenChange,
}: {
  children: React.ReactNode
  open: boolean
  onOpenChange?: (open: boolean) => void
  overlayAll?: boolean
}) {
  const isSm = useMediaQuery(smMediaQuery)
  return isSm ? (
    <Dialog open={open} onOpenChange={onOpenChange}>
      {children}
    </Dialog>
  ) : (
    <Drawer open={open} onOpenChange={onOpenChange} shouldScaleBackground>
      {children}
    </Drawer>
  )
}

function ModalContent({ children, overlayAll }: { children: React.ReactNode; overlayAll?: boolean }) {
  const isSm = useMediaQuery(smMediaQuery)

  return isSm ? (
    <DialogContent className="z-1000 p-8" variant="plain" overlayAll={overlayAll}>
      {children}
    </DialogContent>
  ) : (
    <DrawerContent className="px-6 pb-8">{children}</DrawerContent>
  )
}

function ModalHeader({ children, className, ...props }: { children: React.ReactNode; className?: string }) {
  const isSm = useMediaQuery(smMediaQuery)

  if (isSm) {
    return (
      <DialogHeader
        className={cn(
          'modal-header h-auto border-0 pb-4 pl-0 pr-0 pt-0 text-lg font-semibold text-black-700 dark:text-white-500',
          className,
        )}
        {...props}
      >
        {children}
      </DialogHeader>
    )
  }

  return (
    <DrawerHeader className={cn('modal-header mb-5 flex flex-col p-0 text-left', className)} {...props}>
      {children}
    </DrawerHeader>
  )
}

function ModalTitle({ children, className, ...props }: any) {
  const isSm = useMediaQuery(smMediaQuery)

  if (isSm) {
    return (
      <DialogTitle className="modal-title text-lg font-semibold text-black-700 dark:text-white-500" {...props}>
        {children}
      </DialogTitle>
    )
  }

  return (
    <DrawerTitle className="modal-title text-lg font-semibold text-black-700 dark:text-white-500" {...props}>
      {children}
    </DrawerTitle>
  )
}

function ModalDescription({ children, ...props }: any) {
  const isSm = useMediaQuery(smMediaQuery)

  if (isSm) {
    return (
      <DialogDescription className="modal-description text-sm text-ui-light-1000 dark:text-ui-dark-1000" {...props}>
        {children}
      </DialogDescription>
    )
  }

  return (
    <DrawerDescription className="modal-description text-sm text-ui-light-1000 dark:text-ui-dark-1000">
      {children}
    </DrawerDescription>
  )
}

function ModalFooter({ children }: { children: React.ReactNode }) {
  return (
    <div className="modal-footer mt-8 flex flex-col space-y-4 sm:flex-row sm:justify-end sm:space-x-2 sm:space-y-0">
      {children}
    </div>
  )
}

export { Modal, ModalContent, ModalHeader, ModalTitle, ModalDescription, ModalFooter }
