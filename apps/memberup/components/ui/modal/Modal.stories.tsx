import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'
import { useState } from 'react'

import { Button } from '../button'
import { FormItem } from '../form'
import { Input } from '../input'
import { Modal, ModalContent, ModalDescription, ModalFooter, ModalHeader, ModalTitle } from './Modal'

const meta: Meta<typeof Modal> = {
  title: 'UI/Modal',
  component: Modal,
  parameters: {
    layout: 'centered',
  },
}

export default meta

type Story = StoryObj<typeof meta>

const ModalTrigger = () => {
  const [modalOpen, setModalOpen] = useState(false)
  const [emailValue, setEmailValue] = useState('')
  const [confirmEmailValue, setConfirmEmailValue] = useState('')

  return (
    <>
      <Button variant="default" onClick={() => setModalOpen(true)}>
        Open modal
      </Button>
      <Modal open={modalOpen} onOpenChange={(value) => value !== modalOpen && setModalOpen(value)}>
        <ModalContent>
          <ModalHeader>
            <ModalTitle>Change email</ModalTitle>
            <ModalDescription>
              <p>We will send you a confirmation link to your original email.</p>
            </ModalDescription>
          </ModalHeader>
          <div className="flex flex-col space-y-5">
            <FormItem>
              <Input
                className="w-full"
                placeholder="Email"
                value={emailValue}
                onChange={(e) => setEmailValue(e.target.value)}
              />
            </FormItem>
            <FormItem>
              <Input
                className="w-full"
                placeholder="Confirm email"
                value={confirmEmailValue}
                onChange={(e) => setConfirmEmailValue(e.target.value)}
              />
            </FormItem>
          </div>
          <ModalFooter>
            <Button className="w-full sm:w-32" variant="outline" onClick={() => setModalOpen(false)}>
              Cancel
            </Button>
            <Button className="w-full sm:w-32" variant="default" onClick={() => setModalOpen(false)}>
              Send
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </>
  )
}

const ModalTemplate: Story = {
  render: () => {
    return <ModalTrigger />
  },
}

export const Default: Story = {
  ...ModalTemplate,
}
