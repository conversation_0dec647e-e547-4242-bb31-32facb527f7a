import type { <PERSON><PERSON>, StoryObj } from '@storybook/react'

import { Button } from './Button'

const meta: Meta<typeof Button> = {
  title: 'UI/Button',
  component: Button,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    children: {
      control: {
        type: 'text',
      },
    },
    variant: {},
  },
  args: {
    children: 'Button text',
    size: 'default',
  },
}

export default meta

type Story = StoryObj<typeof meta>

const ButtonTemplate: Story = {
  render: ({ children, ...args }) => {
    return <Button {...args}>{children}</Button>
  },
}

export const Default: Story = {
  ...ButtonTemplate,
  args: {
    variant: 'default',
  },
}

export const Destructive: Story = {
  ...ButtonTemplate,
  args: {
    variant: 'destructive',
  },
}

export const Outline: Story = {
  ...ButtonTemplate,
  args: {
    variant: 'outline',
  },
}

export const Secondary: Story = {
  ...ButtonTemplate,
  args: {
    variant: 'secondary',
  },
}

export const Inline: Story = {
  ...ButtonTemplate,
  args: {
    variant: 'inline',
  },
}

export const Sm: Story = {
  ...ButtonTemplate,
  args: {
    size: 'sm',
  },
}

export const Xs: Story = {
  ...ButtonTemplate,
  args: {
    size: 'xs',
  },
}
