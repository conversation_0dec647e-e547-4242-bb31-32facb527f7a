'use client'

import { Slot } from '@radix-ui/react-slot'
import { cva, type VariantProps } from 'class-variance-authority'
import * as React from 'react'

import { LoaderCircle24Icon } from '@/components/icons'
import { cn } from '@/lib/utils'

const buttonVariants = cva(
  'inline-flex items-center justify-center whitespace-nowrap text-sm font-semibold transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-80 select-none',
  {
    variants: {
      variant: {
        'community-primary': 'bg-community-primary/80 hover:bg-primary text-pure-white hover:bg-community-primary/90',
        default:
          'bg-primary-100 hover:bg-primary-200 disabled:bg-grey-200 disabled:text-grey-400 dark:disabled:bg-grey-800 dark:disabled:text-grey-700 text-pure-white',
        destructive: 'bg-red-100 text-destructive-foreground shadow-sm hover:bg-red-200',
        'outline-destructive': 'border border-red-200 hidden text-red-200',
        disabled:
          'disabled:opacity-100 bg-grey-200 hover:bg-grey-200 dark:bg-grey-800 dark:hover:bg-grey-800 text-grey-400 dark:text-grey-700',
        outline:
          'bg-transparent border border-ui-light-1000 dark:border-black-100 text-ui-light-1000 dark:text-black-100 hover:bg-ui-light-1000/10 dark:hover:bg-black-100/10',
        secondary:
          'bg-ui-light-1000/80 dark:bg-grey-800/80 text-pure-white dark:text-ui-dark-1000 hover:bg-ui-light-1000 hover:dark:bg-grey-800',
        inline: 'bg-transparent border-transparent',
      },
      shape: {
        default: '',
        circular: '',
      },
      size: {
        default: '',
        sm: '',
        xs: '',
      },
    },
    defaultVariants: {
      variant: 'default',
      shape: 'default',
      size: 'default',
    },
    compoundVariants: [
      {
        variant: [
          'community-primary',
          'default',
          'destructive',
          'disabled',
          'outline',
          'outline-destructive',
          'secondary',
        ],
        shape: 'default',
        size: 'default',
        class: 'h-12 px-6 py-4 rounded-base',
      },
      {
        variant: [
          'community-primary',
          'default',
          'destructive',
          'disabled',
          'outline',
          'outline-destructive',
          'secondary',
        ],
        shape: 'default',
        size: 'sm',
        class: 'h-10 px-6 py-3 rounded-base',
      },
      {
        variant: [
          'community-primary',
          'default',
          'destructive',
          'disabled',
          'outline',
          'outline-destructive',
          'secondary',
        ],
        shape: 'default',
        size: 'xs',
        class: 'h-8 px-6 py-2 rounded-base',
      },
      {
        variant: [
          'community-primary',
          'default',
          'destructive',
          'disabled',
          'outline',
          'outline-destructive',
          'secondary',
        ],
        shape: 'circular',
        size: 'default',
        class: 'h-12 w-12 rounded-full flex justify-center items-center',
      },
      {
        variant: [
          'community-primary',
          'default',
          'destructive',
          'disabled',
          'outline',
          'outline-destructive',
          'secondary',
        ],
        shape: 'circular',
        size: 'sm',
        class: 'h-10 w-10 rounded-full flex justify-center items-center',
      },
      {
        variant: [
          'community-primary',
          'default',
          'destructive',
          'disabled',
          'outline',
          'outline-destructive',
          'secondary',
        ],
        shape: 'circular',
        size: 'xs',
        class: 'h-8 w-8 rounded-full flex justify-center items-center',
      },
    ],
  },
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ children, className, variant, shape, size, loading, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : 'button'

    return (
      <Comp className={cn(buttonVariants({ variant, shape, size, className }))} ref={ref} {...props}>
        {loading && <LoaderCircle24Icon className="mr-2 h-[1.125rem] w-[1.125rem] animate-spin" />}
        {children}
      </Comp>
    )
  },
)
Button.displayName = 'Button'

export { Button, buttonVariants }
