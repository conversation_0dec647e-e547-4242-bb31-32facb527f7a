import { <PERSON>a, <PERSON>Obj } from '@storybook/react'
import { useState } from 'react'

import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from './Select'

const meta: Meta<typeof Select> = {
  title: 'ui/Select',
  component: Select,
  argTypes: {},
  parameters: {
    layout: 'centered',
  },
}
export default meta

type Story = StoryObj<typeof Select>

const SelectWithState = ({ disabled = false, error = false }: { disabled?: boolean; error?: boolean }) => {
  const [value, setValue] = useState<string | null>(null)

  return (
    <Select
      className="w-72"
      disabled={disabled}
      error={error}
      placeholder="Select a fruit"
      value={value}
      onValueChange={setValue}
    >
      <SelectTrigger>
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          <SelectItem value="select"></SelectItem>
          <SelectLabel>Fruits</SelectLabel>
          <SelectItem value="apple">Apple</SelectItem>
          <SelectItem value="banana">Banana</SelectItem>
          <SelectItem value="blueberry">Blueberry</SelectItem>
          <SelectItem value="grapes">Grapes</SelectItem>
          <SelectItem value="pineapple">Pineapple</SelectItem>
          <SelectItem value="strawberry">Strawberry</SelectItem>
          <SelectItem value="orange">Orange</SelectItem>
          <SelectItem value="pear">Pear</SelectItem>
          <SelectItem value="kiwi">Kiwi</SelectItem>
          <SelectItem value="watermelon">Watermelon</SelectItem>
          <SelectItem value="peach">Peach</SelectItem>
          <SelectItem value="mango">Mango</SelectItem>
          <SelectItem value="cherry">Cherry</SelectItem>
          <SelectItem value="grapefruit">Grapefruit</SelectItem>
          <SelectItem value="plum">Plum</SelectItem>
        </SelectGroup>
        <SelectSeparator />
        <SelectGroup>
          <SelectLabel>Vegetables</SelectLabel>
          <SelectItem value="aubergine">Aubergine</SelectItem>
          <SelectItem value="broccoli">Broccoli</SelectItem>
          <SelectItem value="carrot" disabled>
            Carrot
          </SelectItem>
          <SelectItem value="courgette">Courgette</SelectItem>
          <SelectItem value="leek">Leek</SelectItem>
          <SelectItem value="spinach">Spinach</SelectItem>
          <SelectItem value="potato">Potato</SelectItem>
          <SelectItem value="tomato">Tomato</SelectItem>
          <SelectItem value="cucumber">Cucumber</SelectItem>
          <SelectItem value="pepper">Pepper</SelectItem>
          <SelectItem value="onion">Onion</SelectItem>
          <SelectItem value="garlic">Garlic</SelectItem>
          <SelectItem value="celery">Celery</SelectItem>
          <SelectItem value="lettuce">Lettuce</SelectItem>
          <SelectItem value="corn">Corn</SelectItem>
        </SelectGroup>
      </SelectContent>
    </Select>
  )
}

export const Base: Story = {
  render: () => <SelectWithState />,
}

export const Error: Story = {
  render: () => <SelectWithState error />,
}

export const Disabled: Story = {
  render: () => <SelectWithState disabled />,
}
