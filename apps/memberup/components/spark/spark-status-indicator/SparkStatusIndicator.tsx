import React from 'react'

import { Clock16Icon } from '@/components/icons'
import { SparkExpirationCounter } from '@/components/spark/spark-expiration-counter'
import { cn } from '@/lib/utils'

export function SparkStatusIndicator({
  hideExpires = false,
  rightContent,
  streak,
}: {
  hideExpires?: boolean
  rightContent?: React.ReactNode
  streak?: number
}) {
  return (
    <div className="spark-status-indicator tailwind-component inline-block rounded-full bg-[#91939b] px-4 py-[0.375rem] text-xs leading-4 text-pure-white dark:bg-[#8D94A31F]">
      <div className="flex h-5 grow-0 items-center">
        {!hideExpires && (
          <div className="flex h-full items-center min-[390px]:pr-5">
            <Clock16Icon className="mr-1 h-4 w-4" />
            <span className="font-semibold">Expires:</span>
            &nbsp;
            <span className="tailwind-component spark-expiration-counter font-semibold">
              <SparkExpirationCounter />
            </span>
          </div>
        )}
        {(streak !== undefined || rightContent) && !hideExpires && (
          <div className="h-4 w-px bg-pure-white opacity-60 dark:bg-ui-dark-1000 dark:opacity-100 max-[390px]:hidden" />
        )}
        <div
          className={cn(
            'flex h-full items-center justify-between font-semibold',
            !hideExpires && 'pl-6 max-[390px]:hidden',
          )}
        >
          {rightContent}
          {streak !== undefined && (
            <>
              🔥 Your streak:&nbsp;<span className="font-semibold">{streak}</span>
            </>
          )}
        </div>
      </div>
    </div>
  )
}
