import { useEffect, useState } from 'react'

import { useStore } from '@/hooks/useStore'
import { useMounted } from '@/shared-components/hooks/use-mounted'
import { getExpires } from '@/shared-libs/date-utils'
import { DEFAULT_SPARK_EXPIRE_TIME, DEFAULT_SPRK_TIMEZONE } from '@/shared-settings/spark'

export function SparkExpirationCounter() {
  const mountedRef = useMounted(true)
  const membershipSetting = useStore((state) => state.community.membership.membership_setting)

  const [expires, setExpires] = useState(
    getExpires(
      membershipSetting?.spark_expire_time || DEFAULT_SPARK_EXPIRE_TIME,
      membershipSetting?.time_zone || DEFAULT_SPRK_TIMEZONE,
      true,
    ),
  )

  useEffect(() => {
    const intervalId = setInterval(() => {
      const temp = getExpires(
        membershipSetting?.spark_expire_time || DEFAULT_SPARK_EXPIRE_TIME,
        membershipSetting?.time_zone || DEFAULT_SPRK_TIMEZONE,
        true,
      )

      if (mountedRef.current) {
        setExpires(temp)
      }

      if (temp === 'Expired') {
        clearInterval(intervalId)
      }
    }, 1000)

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [membershipSetting?.spark_expire_time, membershipSetting?.time_zone])

  return <span className="inline-block w-[3.375rem] text-left">{expires}</span>
}
