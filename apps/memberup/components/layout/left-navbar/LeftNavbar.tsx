import { usePathname } from 'next/navigation'

import { CreateYourCommunityNavbar } from './create-your-community-navbar'
import { LegalNavbar } from './legal-navbar'
import { AccountSettingsNavigationMenu, CommunityNavigationMenu } from '@/components/layout/navigation-menus'
import { useStore } from '@/hooks/useStore'
import { legalLinks } from '@/lib/constants'

export function LeftNavbar() {
  const pathname = usePathname()
  const user = useStore((state) => state.auth.user)
  const isLegalPage = legalLinks.some((link) => pathname.startsWith(link.href))

  if (!user && pathname.startsWith('/settings/account/')) {
    return null
  }

  const renderLeftNavbarContents = () => {
    if (isLegalPage) {
      return <LegalNavbar />
    }

    if (pathname.startsWith('/settings/account/')) {
      return <AccountSettingsNavigationMenu />
    }

    if (pathname.startsWith('/@') || pathname.startsWith('/chat')) {
      return <CreateYourCommunityNavbar />
    }

    return <CommunityNavigationMenu />
  }

  return <div className="hidden w-[15.5rem] shrink-0 grow-0 xl:flex">{renderLeftNavbarContents()}</div>
}
