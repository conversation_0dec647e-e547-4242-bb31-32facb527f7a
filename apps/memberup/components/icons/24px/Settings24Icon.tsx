import React from 'react'

import { CommunityGradientStops } from '@/components/layout/community-gradient-stops'

type Settings24IconProps = React.SVGProps<SVGSVGElement> & {
  gradient?: boolean
}

export function Settings24Icon({ gradient, ...props }: Settings24IconProps) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      className="chevron-left"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M10.8976 2H13.0201C13.0843 2.00298 13.1406 2.01032 13.2197 2.02755C13.8002 2.15394 14.0209 2.36236 14.5611 3.63884L14.6491 3.84941L14.9886 4.69059L15.0165 4.70207L15.4539 4.50406C16.4066 4.07807 16.8976 3.89863 17.2278 3.84857L17.2962 3.83978L17.4209 3.83305C17.7182 3.83305 18.021 3.93836 18.2528 4.12171L18.3624 4.21976L19.7193 5.57106L19.8137 5.67076C20.2399 6.14029 20.2797 6.47316 19.8082 7.68634L19.6513 8.07772L19.2815 8.95268L19.2886 8.96977L20.1357 9.28611L20.5441 9.44751C21.599 9.87883 21.8158 10.1087 21.9568 10.6367C21.9852 10.7432 21.9953 10.8158 21.9986 10.9058L22 11.0036V12.9113C22 13.0469 21.9954 13.1235 21.962 13.2575C21.8216 13.8213 21.6132 14.0359 20.3455 14.5714L20.1363 14.6587L19.301 14.9959L19.2952 15.0099L19.5972 15.6698C20.3484 17.3393 20.3746 17.7053 19.8809 18.2551L19.8192 18.3221L18.4263 19.7139C18.1769 19.966 17.8217 20.1048 17.4779 20.1048C17.186 20.1048 16.7275 19.966 15.9459 19.6526L15.5275 19.4812L15.0509 19.2796L15.0236 19.2909L14.7773 19.9514C14.1744 21.54 13.9678 21.7979 13.3484 21.9577C13.2569 21.9814 13.1914 21.9914 13.1171 21.9955L12.9945 21.9981H11.0856C10.9665 21.9981 10.8993 21.9945 10.7809 21.9689C10.2007 21.8431 9.97914 21.6342 9.43788 20.357L9.34966 20.1463L9.0096 19.3047L8.98344 19.2939L8.34815 19.5794C7.332 20.0268 6.89179 20.1629 6.57806 20.1629C6.28426 20.1629 5.98508 20.0597 5.74708 19.8746L5.63349 19.7753L4.27843 18.422L4.12369 18.2533C3.75208 17.8161 3.73381 17.477 4.18994 16.31L4.42762 15.7258L4.71777 15.0438L4.71172 15.0291L4.05484 14.7843C2.73708 14.2843 2.33098 14.054 2.138 13.6335L2.0858 13.5012L2.0422 13.3547C2.01752 13.261 2.00699 13.1939 2.00272 13.1176L2 12.9917V11.0828C2 10.9461 2.00474 10.8688 2.03877 10.7337C2.16566 10.2297 2.34576 10.0058 3.29447 9.58072L3.68766 9.41092L4.69733 9.00262L4.70425 8.98581L4.32674 8.15288C3.70383 6.73802 3.65224 6.30722 4.04525 5.82607L4.09903 5.76294L4.22636 5.62798L5.57616 4.28124C5.82346 4.03257 6.1772 3.89238 6.5215 3.89238C6.76776 3.89238 7.12934 3.98982 7.70805 4.20901L8.29026 4.43981L8.95028 4.71643L8.97734 4.70517L9.22549 4.04036C9.83405 2.44037 10.0513 2.1728 10.6932 2.0291C10.7742 2.01097 10.8319 2.00324 10.8976 2ZM12.523 3.99811H11.4081L11.2509 4.36664C11.1912 4.51314 11.1238 4.68428 11.0478 4.88292L10.6993 5.81504C10.6157 6.03573 10.4574 6.21851 10.2538 6.33283L10.1483 6.38415L9.3292 6.725C9.1224 6.81106 8.89398 6.82472 8.6804 6.76611L8.55432 6.72227L7.58525 6.31379L7.06181 6.10506L6.70998 5.9752L5.91977 6.76362L6.01277 6.99853C6.04911 7.08701 6.09094 7.18593 6.13863 7.29613L6.2997 7.66141L6.70366 8.5445C6.80042 8.7548 6.82065 8.99074 6.76353 9.21221L6.71997 9.34304L6.38503 10.157C6.29635 10.3724 6.13575 10.549 5.93242 10.6579L5.82733 10.7066L4.73976 11.1438L4.19609 11.3768L4 11.4676V12.5978L4.23463 12.6995C4.32346 12.7367 4.42321 12.7772 4.53477 12.8214L4.90579 12.9653L5.80732 13.3001C6.03461 13.3821 6.22321 13.543 6.34018 13.7513L6.39255 13.8593L6.72754 14.6709C6.81356 14.8793 6.82615 15.1093 6.76566 15.3238L6.7206 15.4504L6.33182 16.3627L6.088 16.9645L5.96921 17.2841L6.75948 18.0733L6.99479 17.9803C7.08146 17.945 7.17645 17.9054 7.28 17.8612L8.00585 17.5412L8.5293 17.3002C8.74835 17.1955 8.99663 17.1747 9.22796 17.2389L9.342 17.2781L10.1659 17.6183C10.3843 17.7085 10.5625 17.8725 10.6707 18.0797L10.7189 18.1868L10.9239 18.7042L11.2157 19.4138L11.3846 19.8015L11.4762 19.9981H12.5944L12.7541 19.6217L12.9621 19.0905L13.3007 18.1825C13.3843 17.9598 13.5438 17.7755 13.7492 17.6607L13.8556 17.6093L14.676 17.2707C14.8804 17.1864 15.1058 17.1726 15.317 17.2294L15.4417 17.2719L16.2143 17.5996L16.7781 17.8292L17.0867 17.9482L17.2903 18.0219L18.0815 17.2322L17.929 16.8559L17.7004 16.3344L17.2956 15.452C17.1985 15.2413 17.1782 15.0049 17.2356 14.783L17.2793 14.6519L17.6137 13.8415C17.7019 13.6277 17.861 13.4522 18.0624 13.3433L18.1665 13.2945L19.0876 12.9243L19.5578 12.7271L19.6871 12.6709L20 12.5278V11.3987L19.6863 11.2648L19.2615 11.0967L18.1891 10.6975C17.9623 10.615 17.7744 10.4538 17.658 10.2454L17.6059 10.1374L17.2715 9.32263C17.1862 9.11475 17.1738 8.8855 17.2341 8.67166L17.279 8.5455L17.5895 7.81961C17.6703 7.62765 17.7406 7.45742 17.8014 7.30696L17.9103 7.03183L18.029 6.71032L17.2384 5.92293L17.0029 6.01609C16.8727 6.06919 16.7235 6.13221 16.5542 6.2058L16.3782 6.28294L15.4554 6.70151C15.2454 6.79771 15.0101 6.81772 14.7891 6.76074L14.6586 6.71731L13.8352 6.37885C13.6196 6.29025 13.443 6.1297 13.334 5.9264L13.2853 5.82132L12.9942 5.09293L12.7832 4.58263L12.6663 4.31124L12.523 3.99811ZM12.0005 7.99811C14.2066 7.99811 16 9.79113 16 11.9972C16 14.2035 14.2065 15.9981 12.0005 15.9981C9.79382 15.9981 8 14.2038 8 11.9972C8 9.79081 9.79369 7.99811 12.0005 7.99811ZM12.0005 9.99811C10.898 9.99811 10 10.8956 10 11.9972C10 13.0994 10.8985 13.9981 12.0005 13.9981C13.1016 13.9981 14 13.0992 14 11.9972C14 10.8958 13.1021 9.99811 12.0005 9.99811Z"
        fill={gradient ? 'url(#community-gradient)' : 'currentColor'}
      />
      {gradient && (
        <defs>
          <linearGradient
            id="community-gradient"
            x1="2"
            y1="11.99905"
            x2="22"
            y2="11.99905"
            gradientUnits="userSpaceOnUse"
          >
            <CommunityGradientStops />
          </linearGradient>
        </defs>
      )}
    </svg>
  )
}
