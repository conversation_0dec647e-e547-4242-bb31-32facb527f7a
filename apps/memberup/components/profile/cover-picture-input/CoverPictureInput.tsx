import { useFilePicker } from 'use-file-picker'
import { ImageDimensionsValidator } from 'use-file-picker/validators'

import { Delete20Icon, Photo20Icon, PhotoAdd24Icon } from '@/components/icons'
import { CroppedImage } from '@/components/images/cropped-image'
import { ImageCropperDialog } from '@/components/images/image-cropper-dialog'
import { AspectRatio, Button } from '@/components/ui'
import { coverPictureMinHeight, coverPictureMinWidth } from '@/lib/constants'
import { CropArea } from '@/shared-types/types'

function CoverPictureInput({
  src,
  cropArea,
  disabled,
  onRemovePicture,
  onSelectPicture,
  alt,
}: {
  alt?: string
  disabled?: boolean
  src?: string
  cropArea?: any
  onRemovePicture: () => void
  onSelectPicture: (src: string, cropArea: CropArea, file: any) => void
}) {
  const minWidth = coverPictureMinWidth
  const minHeight = coverPictureMinHeight

  const { clear, errors, openFilePicker, plainFiles } = useFilePicker({
    accept: 'image/*',
    multiple: false,
    readAs: 'DataURL',
    validators: [
      new ImageDimensionsValidator({
        minWidth,
        minHeight,
      }),
    ],
  })

  const imageCropperSettings =
    plainFiles?.[0]?.type?.indexOf('image') >= 0
      ? {
          url: URL.createObjectURL(plainFiles[0]),
          file: plainFiles[0],
          crop_area: {
            x: 0,
            y: 0,
            width: 0,
            height: 0,
          },
        }
      : null

  const onCropComplete = (cropArea: CropArea, croppedImageBlob: string) => {
    clear()
    onSelectPicture(croppedImageBlob, cropArea, imageCropperSettings.file)
  }

  return (
    <>
      <AspectRatio
        ratio={16 / 9}
        className="overflow-hidden rounded-xl bg-white-100 dark:bg-black-300"
        onClick={() => !src && openFilePicker()}
      >
        {src ? (
          <>
            <CroppedImage src={src} cropArea={cropArea} width={400} height={225} alt={alt} />
            <div className="absolute right-4 top-4 flex space-x-4">
              <Button
                className="bg-black-700 hover:bg-black-600 dark:hover:bg-black-600"
                size="sm"
                variant="default"
                disabled={disabled}
                onClick={(e) => {
                  e.preventDefault()
                  openFilePicker()
                }}
              >
                <Photo20Icon className="mr-0.5" />
                Change
              </Button>
              <Button
                className="bg-black-700 hover:bg-black-600"
                variant="outline-destructive"
                shape="circular"
                title="Remove"
                size="sm"
                disabled={disabled}
                onClick={(e) => {
                  e.preventDefault()
                  onRemovePicture()
                }}
              >
                <Delete20Icon />
              </Button>
            </div>
          </>
        ) : (
          <div className="flex h-full w-full cursor-pointer select-none items-center justify-center">
            <div className="flex flex-col items-center text-sm">
              <PhotoAdd24Icon className="mb-4 text-black-100" />
              <div className="font-medium text-black-700 dark:text-white-500">Upload Cover Photo</div>
              <div className="mt-3.5 font-semibold text-primary-100">Select image</div>
            </div>
          </div>
        )}
      </AspectRatio>
      {imageCropperSettings && (
        <ImageCropperDialog
          aspectRatio={minWidth / minHeight}
          url={imageCropperSettings.url}
          open={true}
          file={imageCropperSettings.file}
          minWidth={minWidth}
          minHeight={minHeight}
          onCropComplete={onCropComplete}
          onClose={() => clear()}
        />
      )}
      {errors && errors.length > 0 && (
        <p className="pt-3 text-sm text-red-200">
          Please select an image that is at least {minWidth} x {minHeight} pixels.
        </p>
      )}
    </>
  )
}

export { CoverPictureInput }
