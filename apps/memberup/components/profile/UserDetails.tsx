import { PERSONALITY_TYPE_ENUM } from '@prisma/client'
import { AspectRatio } from '@radix-ui/react-aspect-ratio'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'

import { getDateTimeFromNow } from '@memberup/shared/src/libs/date-utils'
import { getFullName } from '@memberup/shared/src/libs/profile'
import { IUser } from '@memberup/shared/src/types/interfaces'
import { MBTIBadge } from '@/components/badges'
import { Brain16Icon, Calendar16Icon, Clock16Icon } from '@/components/icons'
import { CroppedImage } from '@/components/images/cropped-image'
import { ProfilePicture } from '@/components/images/profile-picture'
import { PoweredBy } from '@/components/layout'
import { SocialLinks } from '@/components/social'
import { buttonVariants, Separator } from '@/components/ui'
import { StickyContainer } from '@/components/ui/sticky-container/StickyContainer'
import { useStore } from '@/hooks/useStore'
import { cn } from '@/lib/utils'
import { TAppCropArea } from '@/shared-types/types'

function UserDetails({ children, className, user }: { children: React.ReactNode; className?: string; user: IUser }) {
  const fullName = getFullName(user.first_name, user.last_name)
  const currentUser = useStore((state) => state.auth.user)

  const showChatButton = currentUser && currentUser.id !== user.id

  return (
    <StickyContainer
      className={cn('user-details mb-4 w-full shrink-0 md:mb-0 md:block md:w-72 xl:w-[19rem]', className)}
    >
      <div className="rounded-container overflow-hidden">
        <div className="relative mb-5 flex flex-col items-center">
          {user.profile.cover_image ? (
            <CroppedImage
              className="overflow-hidden"
              src={user.profile.cover_image}
              cropArea={user.profile.cover_image_crop_area as TAppCropArea}
              width={800}
              height={450}
              alt={`${fullName} cover photo`}
            />
          ) : (
            <AspectRatio ratio={16 / 9} className="overflow-hidden">
              <Image
                className="h-full w-full object-cover"
                src="/img/cover-photo-placeholder.jpg"
                width={800}
                height={450}
                alt="Profile cover"
              />
            </AspectRatio>
          )}
          <div className="profile-picture-container mt-[calc(-70px_-_1vw)] flex h-[w-140px] w-[140px] items-center justify-center overflow-hidden rounded-full min-[440px]:-mt-[16.5vw] min-[440px]:w-[31vw] sm:-mt-[15.5vw] sm:w-[29vw] md:-mt-[72px] md:h-32 md:w-32">
            <AspectRatio ratio={1}>
              <ProfilePicture
                className="h-full w-full"
                src={user.profile.image}
                cropArea={user.profile.image_crop_area}
                width={128}
                height={128}
                alt={fullName}
              />
            </AspectRatio>
          </div>
        </div>
        <div className="px-5 pb-5 text-center">
          <h1 className="mb-2 text-lg font-semibold">{fullName}</h1>
          <p className="mb-2 break-words text-xs font-medium text-black-100 dark:text-black-200">
            @{user.username}
            {user.profile.location && ` • ${user.profile.location}`}
          </p>
          <p className="mb-3 break-words text-sm text-black-600 last:mb-0 dark:text-white-200">{user.profile.bio}</p>
          <SocialLinks
            className="text flex justify-center space-x-3.5 text-black-200 dark:text-black-100 [&_a:hover]:text-black-100 dark:[&_a:hover]:text-black-200 [&_a]:transition-colors"
            website={user.profile.social?.website}
            facebook={user.profile.social?.facebook}
            instagram={user.profile.social?.instagram}
            linkedin={user.profile.social?.linkedin}
            youtube={user.profile.social?.youtube}
            x={user.profile.social?.x}
            tiktok={user.profile.social?.tiktok}
          />
        </div>
        <Separator />
        <div className="flex flex-col gap-4 py-4 text-ssm text-black-200 dark:text-ui-dark-1000">
          <div className="flex items-center px-5">
            <Clock16Icon className="mr-2" /> Last active {getDateTimeFromNow(user.profile.last_activity_at)}
          </div>
          <div className="flex items-center px-5">
            <Calendar16Icon className="mr-2" /> Joined {getDateTimeFromNow(user.createdAt)}
          </div>
          {user.profile.personality_type && user.profile.personality_type !== PERSONALITY_TYPE_ENUM.DS && (
            <div className="flex items-center px-5">
              <Brain16Icon className="mr-2" />
              <MBTIBadge personalityType={user.profile.personality_type} />
            </div>
          )}
          {showChatButton && (
            <>
              <Separator />
              <div className="px-5">
                <Link href={`/chat?member=${user.id}`} className={cn(buttonVariants({ variant: 'outline' }), 'w-full')}>
                  Chat
                </Link>
              </div>
            </>
          )}
        </div>
        <div>
          {children && (
            <>
              <Separator />
              <div className="p-5">
                {/*
                <div className="flex space-x-8 md:space-x-7 xl:space-x-8 py-5">
                  <div className="text-center">
                    <div className="text-base font-semibold">405</div>
                    <div className="text-xs text-black-200 dark:text-black-100">Contributions</div>
                  </div>
                  <div className="text-center">
                    <div className="text-base font-semibold">2296</div>
                    <div className="text-xs text-black-200 dark:text-black-100">Followers</div>
                  </div>
                  <div className="text-center">
                    <div className="text-base font-semibold">405</div>
                    <div className="text-xs text-black-200 dark:text-black-100">Following</div>
                  </div>
                </div>
                */}
                {children}
              </div>
            </>
          )}
        </div>
      </div>
      <PoweredBy className="hidden md:mt-6 md:flex" />
    </StickyContainer>
  )
}

export { UserDetails }
