import { useFilePicker } from 'use-file-picker'
import { ImageDimensionsValidator } from 'use-file-picker/validators'

import { TAppCropArea } from '@memberup/shared/src/types/types'
import { Favicon } from '@/components/community/favicon'
import { ImageCropperDialog } from '@/components/images/image-cropper-dialog'
import { ProfilePicture } from '@/components/images/profile-picture'
import { Button } from '@/components/ui'
import { profilePictureMinHeight, profilePictureMinWidth } from '@/lib/constants'
import { cn } from '@/lib/utils'
import { CropArea } from '@/shared-types/types'

function ProfilePictureInput({
  alt,
  className,
  cropShape = 'round',
  src,
  cropArea,
  onRemovePicture,
  onSelectPicture,
  variant = 'user',
}: {
  alt?: string
  className?: string
  cropShape?: 'rect' | 'round'
  src?: string
  cropArea?: TAppCropArea
  onSelectPicture: (src: string, cropArea: TAppCropArea, file: File) => void
  onRemovePicture?: () => void
  variant?: 'user' | 'favicon'
}) {
  const minWidth = profilePictureMinWidth
  const minHeight = profilePictureMinHeight

  const { clear, errors, openFilePicker, plainFiles } = useFilePicker({
    accept: 'image/*',
    multiple: false,
    readAs: 'DataURL',
    validators: [
      new ImageDimensionsValidator({
        minWidth,
        minHeight,
      }),
    ],
  })

  const imageCropperSettings =
    plainFiles?.[0]?.type?.indexOf('image') >= 0
      ? {
          url: URL.createObjectURL(plainFiles[0]),
          file: plainFiles[0],
        }
      : null

  const onCropComplete = (cropArea: CropArea, croppedImageBlob: string) => {
    onSelectPicture(croppedImageBlob, cropArea, imageCropperSettings.file)
    clear()
  }

  return (
    <div className={className}>
      <div className="flex">
        <div className="mr-4 h-16 w-16 shrink-0 overflow-hidden">
          {variant === 'user' ? (
            <ProfilePicture alt={alt} src={src} cropArea={cropArea} width={64} height={64} displayInitials={false} />
          ) : (
            <Favicon
              className={cn('mr-4 h-16 w-16 shrink-0 overflow-hidden', !src && 'bg-white-200 dark:bg-grey-200')}
              communityName={alt}
              cropArea={cropArea}
              src={src}
              width={64}
              height={64}
              variant="inverted"
            />
          )}
        </div>
        <div className="flex flex-col items-start justify-center">
          <Button
            className="text-primary-100 transition-colors hover:text-primary-200"
            variant="inline"
            onClick={(e) => {
              e.preventDefault()
              openFilePicker()
            }}
          >
            {src ? 'Change photo' : 'Select a photo'}
          </Button>
          {src && onRemovePicture && (
            <Button
              className="text-red-200 transition-colors hover:text-red-100"
              variant="inline"
              onClick={(e) => {
                e.preventDefault()
                onRemovePicture()
              }}
            >
              Remove
            </Button>
          )}
        </div>
        {imageCropperSettings && (
          <ImageCropperDialog
            aspectRatio={1}
            url={imageCropperSettings.url}
            minWidth={minWidth}
            minHeight={minHeight}
            file={imageCropperSettings.file}
            open={true}
            onCropComplete={onCropComplete}
            onClose={() => clear()}
            cropShape={cropShape}
          />
        )}
      </div>
      {errors && errors.length > 0 && (
        <p className="pt-3 text-sm text-red-200">
          Please select an image that is at least {minWidth} x {minHeight} pixels.
        </p>
      )}
    </div>
  )
}

export { ProfilePictureInput }
