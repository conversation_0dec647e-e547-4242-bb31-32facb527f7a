import Image from 'next/image'
import { forwardRef, memo } from 'react'

import { cloudinaryLoader, generateCloudinaryPretransforms } from '@/lib/cloudinary'
import { cn } from '@/lib/utils'
import { TAppCropArea } from '@/shared-types/types'

interface CroppedImageProps {
  src: string
  className?: string
  cropArea?: TAppCropArea
  priority?: boolean
  width: number
  height: number
  alt?: string
}

const CroppedImage = memo(
  forwardRef<HTMLDivElement, CroppedImageProps>(
    ({ src, className, cropArea, priority = false, width, height, alt }, ref) => {
      const imgUrl = src as string

      // Images can be either blobs or Cloudinary URLs
      const isBlobUrl = imgUrl?.startsWith('blob:') || false

      const aspectRatio = width / height

      if (!imgUrl || imgUrl === 'null' || imgUrl === 'undefined') return null

      const preTransforms = generateCloudinaryPretransforms(cropArea)

      return (
        <div className={cn('cropped-image relative overflow-hidden', className)} ref={ref}>
          <Image
            className="h-full w-full object-cover"
            src={imgUrl}
            alt={alt || ''}
            width={width}
            height={height}
            loader={
              !isBlobUrl
                ? (loaderOptions) => {
                    const options = {
                      ...loaderOptions,
                    }
                    return cloudinaryLoader({ loaderOptions: options, preTransforms, aspectRatio, minHeight: height })
                  }
                : undefined
            }
            priority={priority}
            unoptimized={isBlobUrl} // Disable Next.js optimization for blob URLs
          />
        </div>
      )
    },
  ),
)

CroppedImage.displayName = 'CroppedImage'

export { CroppedImage }
