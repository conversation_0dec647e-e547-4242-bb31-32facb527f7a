import Image from 'next/image'
import { forwardRef } from 'react'

import { User24Icon } from '@/components/icons'
import { CroppedImage } from '@/components/images/cropped-image'
import { cn } from '@/lib/utils'
import { type CropArea } from '@/shared-types/types'

interface ProfilePictureProps {
  alt?: string
  className?: string
  src?: string
  cropArea?: CropArea
  displayInitials?: boolean
  width: number
  height: number
}

const ProfilePicture = forwardRef<HTMLDivElement, ProfilePictureProps>(
  ({ alt, className, src, cropArea, displayInitials = true, height, width, ...rest }, ref) => {
    if (src && cropArea) {
      return (
        <CroppedImage
          className={cn('rounded-full', className)}
          src={src}
          cropArea={cropArea}
          alt={alt}
          ref={ref}
          width={width}
          height={height}
          {...rest}
        />
      )
    }

    return (
      <div
        className={cn(
          'flex h-full w-full select-none items-center justify-center rounded-full bg-grey-200 text-base font-semibold text-black-200 dark:bg-black-300 dark:text-black-100',
          className,
        )}
        ref={ref}
        style={{
          width: `${width / 16}rem !important`,
          height: `${height / 16}rem !important`,
        }}
      >
        {displayInitials ? alt?.replace(/\s+/g, '').slice(0, 2).toUpperCase() : <User24Icon className="h-2/3 w-2/3" />}
      </div>
    )
  },
)

ProfilePicture.displayName = 'ProfilePicture'

export { ProfilePicture }
