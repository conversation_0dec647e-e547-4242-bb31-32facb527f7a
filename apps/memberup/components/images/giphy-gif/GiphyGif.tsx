'use client'

import Image from 'next/image'

import useGifRendition from '@/hooks/useGifRendition'

export function GiphyGif({
  className,
  id,
  width,
  style,
  ...rest
}: {
  className?: string
  id: string
  style?: any
  width: number
}) {
  const rendition = useGifRendition(id, width)

  return rendition ? (
    <Image
      className={className}
      alt="As"
      src={rendition.url}
      style={{ ...style }}
      width={rendition.width}
      height={rendition.height}
      unoptimized
      {...rest}
    />
  ) : null
}
