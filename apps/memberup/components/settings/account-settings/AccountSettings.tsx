'use client'

import { jwtDecode } from 'jwt-decode'
import { useSession } from 'next-auth/react'
import { useRef, useState } from 'react'

import { EmailChangeForm } from '@/components/settings/email-change-form'
import { EmailConfirmCodeForm } from '@/components/settings/email-confirm-code-form'
import { Button, Input } from '@/components/ui'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { useStore } from '@/hooks/useStore'
import useAppCookie from '@/memberup/components/hooks/use-app-cookie'
import { resetUser } from '@/memberup/store/features/userSlice'
import { IUser } from '@/shared-types/interfaces'

enum ChangeEmailStage {
  EnterEmail = 'enter-email',
  EnterCode = 'enter-code',
}

export default function AccountSettings() {
  const autosaveButtonRef = useRef(null)
  const user = useStore((state) => state.auth.user)
  const { getAppCookie, updateAppCookieAuthToken } = useAppCookie()
  const { update: updateSession } = useSession()
  const [modalOpen, setModalOpen] = useState(false)
  const [changeEmailStage, setChangeEmailStage] = useState<ChangeEmailStage>(ChangeEmailStage.EnterEmail)
  const [newUserCredentials, setNewUserCredentials] = useState(null)
  const [requestingEmailChange, setRequestingEmailChange] = useState(false)
  const updateUser = useStore((state) => state.auth.updateUser)

  const resetState = (e?: React.MouseEvent<HTMLButtonElement>) => {
    if (e) e.preventDefault()

    setModalOpen(false)
    setChangeEmailStage(ChangeEmailStage.EnterEmail)
    setRequestingEmailChange(false)
  }

  const onEmailChangeComplete = async (user: IUser) => {
    const authToken = getAppCookie()
    const decodedToken = authToken
      ? jwtDecode<{ email: string; password: string; redirectTo: string }>(authToken)
      : null

    if (decodedToken?.password) {
      updateAppCookieAuthToken(user.email, decodedToken.password)
    }
    updateSession({
      email: user.email,
    })
    updateUser(user)

    autosaveButtonRef.current.click()
  }

  const onEmailChangeStart = (newEmail: string, newPassword: string) => {
    setNewUserCredentials({
      email: newEmail,
      password: newPassword,
    })
    setChangeEmailStage(ChangeEmailStage.EnterCode)
  }

  return (
    <div className="account-settings tailwind-component page-inner-pb">
      <div className="rounded-box w-full max-w-screen-md p-5">
        <h2 className="-mt-1 mb-5 text-lg font-semibold text-black-700 dark:text-white-500">Account Settings</h2>
        <div className="min-h-20">
          {!modalOpen && (
            <Input
              autoComplete="new-password"
              className="mb-8"
              name="zx"
              placeholder="Email"
              value={user.email}
              disabled
            />
          )}
        </div>
        <Button className="w-full" variant="outline" onClick={() => setModalOpen(true)}>
          Change Email
        </Button>
        <Dialog
          open={modalOpen}
          onOpenChange={() => {
            resetState()
          }}
        >
          <DialogContent>
            {changeEmailStage === ChangeEmailStage.EnterEmail && (
              <>
                <DialogHeader>
                  <DialogTitle>Change Email</DialogTitle>
                  <DialogDescription>We will send you a verification email to your new address.</DialogDescription>
                </DialogHeader>
                <EmailChangeForm
                  requestingEmailChange={requestingEmailChange}
                  setRequestingEmailChange={setRequestingEmailChange}
                  onCancel={resetState}
                  onSuccess={onEmailChangeStart}
                />
              </>
            )}
            {changeEmailStage === ChangeEmailStage.EnterCode && (
              <EmailConfirmCodeForm
                autosaveButtonRef={autosaveButtonRef}
                newUserCredentials={newUserCredentials}
                onCancel={resetState}
                onSuccess={onEmailChangeComplete}
              />
            )}
          </DialogContent>
        </Dialog>
      </div>
    </div>
  )
}
