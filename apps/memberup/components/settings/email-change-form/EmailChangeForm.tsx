import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { resetEmailSendCodeApi } from '@memberup/shared/src/services/apis/reset-email.api'
import { Button, Input, PasswordInput } from '@/components/ui'
import { <PERSON>alog<PERSON>ooter, DialogInner } from '@/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { useStore } from '@/hooks/useStore'
import { emailInUseError, formSubmitError, invalidPasswordError } from '@/lib/error-messages'
import { passwordSchema } from '@/lib/validation/zod'
import { useMounted } from '@/shared-components/hooks/use-mounted'

function EmailChangeForm({
  onCancel,
  onSuccess,
  requestingEmailChange,
  setRequestingEmailChange,
}: {
  onCancel: () => void
  onSuccess: (newEmail: string, newPassword: string) => void
  requestingEmailChange: boolean
  setRequestingEmailChange: (value: boolean) => void
}) {
  const user = useStore((state) => state.auth.user)
  const mounted = useMounted()
  const [errorMessage, setErrorMessage] = useState(null)

  const changeEmailSchema = z
    .object({
      password: passwordSchema,
      email: z.string().email(),
    })
    .refine((data) => data.email !== user.email, {
      message: 'New email must be different from current email',
      path: ['email'],
    })

  type ChangeEmailSchemaType = z.infer<typeof changeEmailSchema>

  const emailChangeForm = useForm<ChangeEmailSchemaType>({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      email: '',
      password: '',
    },
    resolver: zodResolver(changeEmailSchema),
  })

  const onEmailChangeSubmit = async (formData: ChangeEmailSchemaType) => {
    setRequestingEmailChange(true)
    setErrorMessage(null)

    try {
      const res = await resetEmailSendCodeApi(formData.email, formData.password)

      if (res.data.success && mounted.current) {
        onSuccess(formData.email, formData.password)
      }
    } catch (error) {
      if (!mounted.current) return

      if (error.response?.status === 400) {
        if (error.response?.status === 400) {
          if (error.response.data?.code === 'INVALID_PASSWORD') {
            emailChangeForm.setError('password', {
              type: 'custom',
              message: invalidPasswordError,
            })
          } else if (error.response.data?.code === 'EMAIL_IN_USE') {
            emailChangeForm.setError('email', {
              type: 'custom',
              message: emailInUseError,
            })
          }
        } else {
          setErrorMessage(formSubmitError)
        }
      }
    } finally {
      if (mounted.current) setRequestingEmailChange(false)
    }
  }

  return (
    <>
      <DialogInner>
        <Form {...emailChangeForm}>
          <div className="space-y-5">
            <FormField
              control={emailChangeForm.control}
              name="password"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <PasswordInput
                      autoComplete="current-password"
                      className="w-full"
                      disabled={requestingEmailChange}
                      name="cp"
                      placeholder="Enter current password"
                      error={Boolean(error)}
                      {...field}
                    />
                  </FormControl>
                  {error && <FormMessage>{error.message}</FormMessage>}
                </FormItem>
              )}
            />
            <FormField
              control={emailChangeForm.control}
              name="email"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      autoComplete="new-password"
                      className="w-full"
                      disabled={requestingEmailChange}
                      name="ce"
                      type="email"
                      placeholder="New email"
                      error={Boolean(error)}
                      {...field}
                    />
                  </FormControl>
                  {error && <FormMessage>{error.message}</FormMessage>}
                </FormItem>
              )}
            />
          </div>
          {errorMessage && <FormMessage className="mt-5">{errorMessage}</FormMessage>}
        </Form>
      </DialogInner>
      <DialogFooter>
        <Button className="mt-3 w-full sm:mt-0 sm:w-32" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          className="w-full sm:w-32"
          variant="default"
          type="submit"
          loading={requestingEmailChange}
          disabled={requestingEmailChange}
          onClick={emailChangeForm.handleSubmit(onEmailChangeSubmit)}
        >
          Continue
        </Button>
      </DialogFooter>
    </>
  )
}

export { EmailChangeForm }
