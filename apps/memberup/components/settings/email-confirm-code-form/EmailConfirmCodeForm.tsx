'use client'

import { zod<PERSON>esolver } from '@hookform/resolvers/zod'
import { usePathname, useRouter } from 'next/navigation'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { resetEmailApi } from '@memberup/shared/src/services/apis/reset-email.api'
import { Button, Input } from '@/components/ui'
import { DialogDescription, DialogFooter, DialogHeader, DialogInner, DialogTitle } from '@/components/ui/dialog'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { toast } from '@/components/ui/sonner'
import { formSubmitError } from '@/lib/error-messages'
import { useMounted } from '@/shared-components/hooks/use-mounted'
import { IUser } from '@/shared-types/interfaces'

const confirmCodeSchema = z.object({
  code: z.string().min(6),
})

type ConfirmCodeSchemaType = z.infer<typeof confirmCodeSchema>

function EmailConfirmCodeForm({
  autosaveButtonRef,
  newUserCredentials,
  onCancel,
  onSuccess,
}: {
  autosaveButtonRef: React.MutableRefObject<HTMLButtonElement>
  newUserCredentials: { email: string; password: string }
  onCancel: () => void
  onSuccess: (user: IUser) => void
}) {
  const router = useRouter()
  const pathname = usePathname()
  const [requestingCodeValidation, setRequestingCodeValidation] = useState(false)
  const [errorMessage, setErrorMessage] = useState(null)
  const mounted = useMounted()

  const emailConfirmCodeForm = useForm<ConfirmCodeSchemaType>({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      code: '',
    },
    resolver: zodResolver(confirmCodeSchema),
  })

  const onEmailChangeSubmit = async (formData: ConfirmCodeSchemaType) => {
    setErrorMessage(null)
    setRequestingCodeValidation(true)

    try {
      const res = await resetEmailApi(formData.code)

      if (res.data.success && mounted.current) {
        onSuccess(res.data.data)
      }
    } catch (err) {
      if (!mounted.current) return

      if (err.response?.status === 400) {
        emailConfirmCodeForm.setError('code', {
          type: 'custom',
          message: 'Invalid verification code',
        })
      } else {
        setErrorMessage(formSubmitError)
      }
      setRequestingCodeValidation(false)
    }
  }

  const onEmailChangeHiddenFormSubmit = () => {
    toast.success('Email changed successfully')
    router.push(pathname === '/settings/account/account' ? '/settings/account/account/a' : '/settings/account/account')
  }

  return (
    <>
      <DialogHeader>
        <DialogTitle>We sent you a code</DialogTitle>
        <DialogDescription>
          <p>Enter it below to verify your new email.</p>
        </DialogDescription>
      </DialogHeader>
      <DialogInner>
        <Form {...emailConfirmCodeForm}>
          <div className="space-y-5">
            <FormField
              control={emailConfirmCodeForm.control}
              name="code"
              render={({ field, fieldState: { error } }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      autoComplete="one-time-code"
                      className="w-full"
                      disabled={requestingCodeValidation}
                      type="text"
                      placeholder="Verification code"
                      error={Boolean(error)}
                      {...field}
                    />
                  </FormControl>
                  {error && <FormMessage>{error.message}</FormMessage>}
                </FormItem>
              )}
            />
          </div>
          {errorMessage && <FormMessage className="mt-5">{errorMessage}</FormMessage>}
        </Form>
        <form
          action="/settings/account/password"
          onSubmit={(event) => {
            event.preventDefault()
            onEmailChangeHiddenFormSubmit()
          }}
        >
          <Input
            className="hidden"
            autoComplete="username"
            name="username"
            type="email"
            placeholder="Email"
            readOnly
            value={newUserCredentials.email}
          />
          <Input
            className="hidden"
            name="password"
            type="password"
            placeholder="Password"
            readOnly
            autoComplete="new-password"
            value={newUserCredentials.password}
          />
          <Button ref={autosaveButtonRef} type="submit" className="hidden">
            Submit
          </Button>
        </form>
      </DialogInner>
      <DialogFooter>
        <Button className="w-full sm:w-32" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          className="mb-3 w-full sm:mb-0 sm:w-32"
          variant="default"
          type="submit"
          loading={requestingCodeValidation}
          disabled={requestingCodeValidation}
          onClick={emailConfirmCodeForm.handleSubmit(onEmailChangeSubmit)}
        >
          Continue
        </Button>
      </DialogFooter>
    </>
  )
}

export { EmailConfirmCodeForm }
