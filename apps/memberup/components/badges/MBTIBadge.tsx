'use client'

import { useState } from 'react'

import { Badge } from '@/components/ui'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { mbtiTypes } from '@/lib/constants'
import { cn } from '@/lib/utils'

export function MBTIBadge({ className, personalityType }: { className?: string; personalityType: string }) {
  const [dateTooltipOpen, setDateTooltipOpen] = useState(false)
  const description = mbtiTypes.find((type) => type.value === personalityType).description

  return (
    <TooltipProvider>
      <Tooltip open={dateTooltipOpen} onOpenChange={setDateTooltipOpen}>
        <TooltipTrigger
          asChild
          // Workaround from: https://github.com/radix-ui/primitives/issues/955#issuecomment-**********
          onClick={() => setDateTooltipOpen((prevOpen) => !prevOpen)}
          // Timeout runs setOpen after onOpenChange to prevent bug on mobile
          onFocus={() => setTimeout(() => setDateTooltipOpen(true), 0)}
          onBlur={() => setDateTooltipOpen(false)}
        >
          <Badge className={cn(className)} variant="secondary">
            {personalityType}
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>{description}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}
