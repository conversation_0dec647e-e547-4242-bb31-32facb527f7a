import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react'

import { SocialLinks } from './SocialLinks'

const meta: Meta<typeof SocialLinks> = {
  title: 'Social/SocialLinks',
  component: SocialLinks,
  parameters: {
    layout: 'centered',
  },
}

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  render: () => {
    const website = 'https://www.memberup.com'
    const facebook = 'https://www.facebook.com/memberup'
    const instagram = 'https://www.instagram.com/memberup'
    const linkedin = 'https://www.instagram.com/memberup'
    const youtube = 'https://www.youtube.com/memberup'
    const x = 'https://www.x.com/memberup'
    const tiktok = 'https://www.tiktok.com/memberup'

    return (
      <SocialLinks
        website={website}
        facebook={facebook}
        instagram={instagram}
        linkedin={linkedin}
        youtube={youtube}
        x={x}
        tiktok={tiktok}
      />
    )
  },
}
