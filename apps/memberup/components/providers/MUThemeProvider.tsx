'use client'

import { CssBaseline } from '@mui/material'
import { AppRouterCacheProvider } from '@mui/material-nextjs/v15-appRouter'
import { createTheme, ThemeOptions, ThemeProvider } from '@mui/material/styles'
import React, { useMemo } from 'react'

import { AppThemeOptions, DefaultThemeOptions } from '@memberup/shared/src/settings/theme'
import { useStore } from '@/hooks/useStore'
import { selectMembershipTheme } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/store'
import { getThemeOptions } from '@/shared-libs/styles'
import { THEME_MODE_ENUM } from '@/shared-types/enum'

export function MUThemeProvider({ children }: { children: React.ReactNode }) {
  const mainColor = useAppSelector((state) => selectMembershipTheme(state))
  const profile = useStore((state) => state.auth.profile)

  const theme = useMemo(() => {
    const themeMode = profile?.theme_mode || THEME_MODE_ENUM.dark

    const themeOptions = ((mainColor && getThemeOptions(themeMode, AppThemeOptions[themeMode], mainColor)) ||
      DefaultThemeOptions) as unknown as ThemeOptions

    return createTheme(themeOptions)
  }, [profile, mainColor])

  return (
    <>
      <AppRouterCacheProvider>
        <ThemeProvider theme={theme}>{children}</ThemeProvider>
      </AppRouterCacheProvider>
      <CssBaseline />
    </>
  )
}
