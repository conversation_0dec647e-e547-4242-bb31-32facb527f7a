import _get from 'lodash/get'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findUserById } from '@memberup/shared/src/libs/prisma/user'
import { generateStripeCustomer } from '@/lib/stripe'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { updateUserProfile } from '@/shared-libs/prisma/user-profile'
import { createRewardfulAffiliate, updateRewardfulAffiliate } from '@/shared-libs/rewardful'

const REWARDFUL_API_SECRET = process.env.REWARDFUL_API_SECRET
const STRIPE_LIVE_MODE = process.env.NEXT_PUBLIC_STRIPE_LIVE_MODE

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Internal server error')
  },
  onNoMatch: (req, res) => {
    res.status(404).end()
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    if (STRIPE_LIVE_MODE !== 'true') {
      return res.json({ success: false })
    }

    const user = req['user']

    const dbUser = await findUserById({
      where: { id: user.id },
      include: {
        profile: true,
      },
    })

    let affiliate = null
    let stripeCustomer = null

    if (!dbUser.profile?.affiliate) {
      if (!dbUser.profile?.stripe_customer_id) {
        stripeCustomer = await generateStripeCustomer(dbUser)
      }

      affiliate = await createRewardfulAffiliate(REWARDFUL_API_SECRET as string, {
        first_name: dbUser.first_name || '_',
        last_name: dbUser.last_name || '_',
        email: dbUser.email,
        stripe_customer_id: stripeCustomer.id,
      })
    }

    const linkUrl = affiliate?.links?.[0]?.url

    // res.json({ success: true, data: { linkUrl }})
    res.json({ success: true, data: { affiliateUrl: 'https://memberup.com' } })
  } catch (err) {
    res.status(400).end(_get(err, ['response', 'data', 'details', 0], 'Unknow Error'))
  }
})

export default handler
