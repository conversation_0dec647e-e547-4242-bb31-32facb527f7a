import _get from 'lodash/get'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { getRewardfulAffiliate } from '@memberup/shared/src/libs/rewardful'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const REWARDFUL_API_SECRET = process.env.REWARDFUL_API_SECRET
const STRIPE_LIVE_MODE = process.env.NEXT_PUBLIC_STRIPE_LIVE_MODE

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    if (STRIPE_LIVE_MODE !== 'true') {
      return res.json({ success: false })
    }

    const user = req['user']

    if (!user.profile?.affiliate?.['id']) {
      return res.json({ success: false })
    }

    const result = await getRewardfulAffiliate(REWARDFUL_API_SECRET, user.profile.affiliate['id'])

    res.json({ success: true, data: result })
  } catch (err) {
    res.status(400).end(_get(err, ['response', 'data', 'details', 0], 'Unknow Error'))
  }
})

export default handler
