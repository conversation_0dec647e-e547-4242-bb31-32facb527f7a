import _get from 'lodash/get'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { getRewardfulCommissions } from '@memberup/shared/src/libs/rewardful'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const REWARDFUL_API_SECRET = process.env.REWARDFUL_API_SECRET
const STRIPE_LIVE_MODE = process.env.NEXT_PUBLIC_STRIPE_LIVE_MODE

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const { state, page, limit, sale, campaign, all } = req.query

    if (STRIPE_LIVE_MODE !== 'true') {
      return res.json({ success: false })
    }

    const user = req['user']

    if (!user.profile?.affiliate?.['id']) {
      return res.json({ success: false })
    }

    const data = []
    let pagination
    do {
      const params = {
        affiliate_id: user.profile.affiliate['id'],
      }

      if (all) {
        params['limit'] = 100
      } else if (limit) {
        params['limit'] = parseInt((limit as string) || '25')
      }

      if (pagination?.next_page) {
        params['page'] = pagination.next_page
      } else {
        params['page'] = parseInt((page as string) || '0')
      }

      if (state) {
        params['state'] = state
      }

      const expand = []
      if (sale) {
        expand.push('sale')
      }
      if (campaign) {
        expand.push('campaign')
      }
      if (expand.length) {
        params['expand'] = expand
      }
      const result = await getRewardfulCommissions(REWARDFUL_API_SECRET, params)
      pagination = result.pagination
      data.push(...result.data)
    } while (all && pagination?.next_page)

    res.json({
      success: true,
      data: {
        data,
        pagination,
      },
    })
  } catch (err) {
    res.status(400).end(_get(err, ['response', 'data', 'details', 0], 'Unknow Error'))
  }
})

export default handler
