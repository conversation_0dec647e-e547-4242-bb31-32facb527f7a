import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findUser } from '@memberup/shared/src/libs/prisma/user'
import { IUser } from '@memberup/shared/src/types/interfaces'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeDetachPaymentMethod } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Internal server error')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const stripeConnectedAccount = user.membership_setting?.stripe_connect_account
    const paymentMethodId = req.query.paymentMethodId as string

    if (!stripeConnectedAccount) {
      return res.status(200).send({ success: true, data: null })
    }

    const dbUser: IUser = await findUser({ where: { id: user.id }, include: { profile: true } })
    const userProfile = dbUser.profile

    if (!userProfile?.stripe_payment_method_id) {
      return res.status(200).send({ success: true, data: null })
    }
    const result = await stripeDetachPaymentMethod(stripeConnectedAccount, paymentMethodId)

    res.status(200).send({ success: true, data: result })
  } catch (err: any) {
    res.status(400).json({ message: err.message })
  }
})

export default handler
