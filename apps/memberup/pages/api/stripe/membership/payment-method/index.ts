import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findUser } from '@memberup/shared/src/libs/prisma/user'
import { updateUserProfile } from '@memberup/shared/src/libs/prisma/user-profile'
import { IUser } from '@memberup/shared/src/types/interfaces'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeGetPaymentMethods, stripeUpdatePaymentMethod } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { type, limit, starting_after } = req.query
      const stripeConnectedAccount = user.membership_setting?.stripe_connect_account

      if (!stripeConnectedAccount) {
        return res.status(200).send({ success: true, data: { has_more: false, data: [] } })
      }

      const dbUser: IUser = await findUser({ where: { id: user.id }, include: { profile: true } })
      const userProfile = dbUser.profile

      if (!userProfile?.stripe_customer_id) {
        return res.status(200).send({ success: true, data: { has_more: false, data: [] } })
      }

      const result = await stripeGetPaymentMethods(stripeConnectedAccount, userProfile.stripe_customer_id, {
        limit: limit ? parseInt(limit as string) : 100,
        starting_after: starting_after as string,
        type: type as any,
      })

      res.status(200).send({ success: true, data: result })
    } catch (err: any) {
      res.status(400).json({ message: err.message })
    }
  })
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { stripe_payment_method_id } = req.body
      const stripeConnectedAccount = user.membership_setting?.stripe_connect_account

      if (!stripeConnectedAccount) {
        return res.status(200).send({ success: false, data: null })
      }

      const dbUser: IUser = await findUser({ where: { id: user.id }, include: { profile: true } })
      let userProfile = dbUser.profile

      if (req.query.set_default === 'true') {
        userProfile = await updateUserProfile({
          where: { id: userProfile.id },
          data: { stripe_payment_method_id },
        })
      }

      res.status(200).send({ success: true, data: userProfile })
    } catch (err: any) {
      res.status(400).json({ message: err.message })
    }
  })
  .put(async (req, res) => {
    try {
      const user = req['user']
      const { billing_details, card } = req.body
      const stripeConnectedAccount = user.membership_setting?.stripe_connect_account
      if (!stripeConnectedAccount) {
        return res.status(200).send({ success: true, data: null })
      }

      const dbUser: IUser = await findUser({ where: { id: user.id }, include: { profile: true } })
      const userProfile = dbUser.profile

      if (!userProfile?.stripe_payment_method_id) {
        return res.status(200).send({ success: true, data: null })
      }

      const result = await stripeUpdatePaymentMethod(stripeConnectedAccount, userProfile.stripe_payment_method_id, {
        billing_details,
        card,
      })
      res.status(200).send({ success: true, data: result })
    } catch (err: any) {
      res.status(400).json({ message: err.message })
    }
  })

export default handler
