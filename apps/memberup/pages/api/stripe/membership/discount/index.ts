import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeCreateCoupon, stripeCreatePromotionCode, stripeDeleteCoupon } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .post(async (req, res) => {
    try {
      const user = req['user']
      const stripeConnectAccount = user.membership_setting?.stripe_connect_account

      if (!stripeConnectAccount) {
        return res.status(400).json({
          message: 'Membership is not configured properly. Please contact the owner.',
        })
      }

      const { amount_off, code, percent_off, max_redemptions } = req.body
      const stripeCoupon = await stripeCreateCoupon(stripeConnectAccount, {
        amount_off,
        percent_off,
        max_redemptions,
      })
      let stripePromotionCode

      if (code) {
        stripePromotionCode = await stripeCreatePromotionCode(stripeConnectAccount, {
          coupon: stripeCoupon.id,
          code,
        })
      }

      return res.status(200).send({
        success: true,
        data: {
          stripe_coupon: stripeCoupon,
          stripe_promotion_code: stripePromotionCode,
        },
      })
    } catch (err: any) {
      console.log('err --------', err)
      res.status(400).json({ message: err.message })
    }
  })
  .delete(async (req, res) => {
    try {
      const user = req['user']
      const stripeConnectAccount = user.membership_setting?.stripe_connect_account
      const { coupon_id } = req.body

      if (!stripeConnectAccount) {
        return res.status(400).json({
          message: 'Membership is not configured properly. Please contact the owner.',
        })
      }

      const result = await stripeDeleteCoupon(stripeConnectAccount, coupon_id)
      return res.status(200).send({
        success: result.deleted,
      })
    } catch (err: any) {
      console.log('err --------', err)
      res.status(400).json({ message: err.message })
    }
  })
export default handler
