import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { updateMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeCreateAccount, stripeCreateAccountLink, stripeGetAccount } from '@/shared-libs/stripe'
import checkCreatorRole from '@/src/middlewares/check-creator-role'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(checkCreatorRole)
  .post(async (req, res) => {
    const user = req['user']
    const userMembership = user.current_user_membership
    const membership = userMembership.membership
    const membershipSetting = membership.membership_setting

    try {
      const host = req.headers.host
      let protocol = host.includes('localhost:3000') ? 'http://' : 'https://'
      const refreshUrl = `${protocol}${host}/${membership.slug}/settings/community-pricing`
      const redirectUrl = `${protocol}${host}/${membership.slug}/settings/stripe-connect-success`

      if (membershipSetting.stripe_connect_account?.access_token || membershipSetting.stripe_connect_account?.enabled) {
        return res.status(409).json({ message: 'Stripe already connected' })
      }

      let stripeAccountId = membershipSetting.stripe_connect_account?.stripe_user_id
      if (!stripeAccountId) {
        const account = await stripeCreateAccount(refreshUrl, redirectUrl)
        stripeAccountId = account.id

        // NOTE: We just create the account but we don't enable yet until the user completes all the verification steps.
        await updateMembershipSetting({
          where: { id: membershipSetting.id },
          data: {
            stripe_connect_account: {
              stripe_user_id: stripeAccountId,
            },
          },
        })
      }

      const account_link = await stripeCreateAccountLink(stripeAccountId, refreshUrl, redirectUrl)
      return res.status(200).send({
        success: true,
        data: { account_link },
      })
    } catch (err) {
      return res.status(500).json({ message: err.message })
    }
  })
  .get(async (req, res) => {
    try {
      const user = req['user']
      const userMembership = user.current_user_membership
      const membershipSetting = userMembership.membership.membership_setting
      const stripeConnectAccount = membershipSetting?.stripe_connect_account
      if (stripeConnectAccount) {
        const connectedAccount = await stripeGetAccount(
          membershipSetting.stripe_live_mode,
          { expand: ['capabilities', 'business_profile', 'external_accounts'] },
          {
            stripeAccount: stripeConnectAccount.stripe_user_id,
          },
        )
        return res.status(200).send({
          success: true,
          data: connectedAccount,
        })
      }
      return res.status(200).send({
        success: true,
        data: null,
      })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(500).json({ message: err.message })
    }
  })

export default handler
