import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import Stripe from 'stripe'

import { STRIPE_APPLICATION_FEE_ENTERPRISE } from '@memberup/shared/src/config/envs'
import { USER_MEMBERSHIP_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status500 } from '@/shared-libs/api-utils'
import prisma from '@/shared-libs/prisma/prisma'
import { findUserMembershipByIds } from '@/shared-libs/prisma/user-membership'
import { stripeCreateSubscription, stripeGetPrices, stripeGetSubscriptions } from '@/shared-libs/stripe'
import { TStripeConnectAccount } from '@/shared-types/types'

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { membership_id: membershipId, include_default_payment_method: includeDefaultPaymentMethod } = req.query
      const userMembership = await prisma.userMembership.findUnique({
        where: {
          user_id_membership_id: {
            user_id: user.id,
            membership_id: membershipId as string,
          },
        },
        include: {
          user: {
            include: {
              profile: true,
            },
          },
          membership: {
            include: {
              membership_setting: true,
            },
          },
        },
      })
      if (!userMembership) {
        return status400(res, 'You need to first join this community.')
      }
      const membership = userMembership.membership
      const membershipSetting = membership.membership_setting
      const connectedStripeAccountInfo = membershipSetting?.stripe_connect_account as TStripeConnectAccount
      const stripeCustomerId = userMembership?.stripe_customer_id

      if (!connectedStripeAccountInfo) {
        return res.status(400).json({
          message: "Membership didn't setup stripe. Please contact the owner.",
        })
      }

      if (!stripeCustomerId) {
        return res.status(400).json({
          message: "You didn't setup stripe.",
        })
      }

      const options = {
        customer: stripeCustomerId,
      }

      if (includeDefaultPaymentMethod) {
        options['expand'] = ['data.default_payment_method']
      }

      const result = await stripeGetSubscriptions(connectedStripeAccountInfo, options)
      return res.status(200).send({ success: true, data: result })
    } catch (error) {
      console.error(error)
      Sentry.captureException(error)
      res.status(500).end()
    }
  })
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { collection_method, default_payment_method, coupon, selected_price_id: selectedPriceId } = req.body
      const { membership_id: membershipId } = req.query

      const userMembership = await findUserMembershipByIds(membershipId, user.id)
      if (!userMembership) {
        return status400(res, `You must first try join the community.`)
      }

      const membership = userMembership.membership
      const membershipSetting = membership.membership_setting
      const connectedStripeAccountInfo: any = membershipSetting?.stripe_connect_account
      const stripeProductId = membershipSetting?.stripe_product_id
      const membershipPrices: any = membershipSetting?.stripe_prices

      if (!connectedStripeAccountInfo || !stripeProductId) {
        return status400(res, `Community didn't setup stripe. Please contact the owner.`)
      }
      if (!membershipPrices || !membershipPrices.filter((price) => price.active).length) {
        return status400(res, `Community owner setup stripe connect but has not enabled any plans`)
      }
      if (!default_payment_method) {
        return status400(res, `Payment Method is not defined`)
      }
      if (userMembership?.stripe_subscription_id) {
        return status400(res, `You already have the subscription to a plan`)
      }
      const stripeCustomerId = userMembership?.stripe_customer_id
      const updateData = {}

      const stripeCreateSubscriptionPayload: Stripe.SubscriptionCreateParams = {
        customer: stripeCustomerId,
        collection_method,
        application_fee_percent: STRIPE_APPLICATION_FEE_ENTERPRISE,
        default_payment_method,
        payment_behavior: 'allow_incomplete',
        metadata: { membership_id: membership.id },
      }
      if (coupon) {
        stripeCreateSubscriptionPayload.coupon = coupon
      }

      const existingStripePricesResult = await stripeGetPrices(connectedStripeAccountInfo, {
        active: true,
        product: stripeProductId,
      })
      const existingStripePrices = existingStripePricesResult.data
      if (!existingStripePrices || !existingStripePrices.length) {
        return status500(res, 'There is no active stripe price. Please ask admin about this.')
      }

      const stripePrice = existingStripePrices.find((p) => p.id === selectedPriceId)
      if (!stripePrice) {
        return status400(res, `The provided price cannot be found.`)
      }

      const stripeSubscription = await stripeCreateSubscription(
        connectedStripeAccountInfo,
        stripeCreateSubscriptionPayload,
        stripePrice.id,
      )

      const latestInvoice = stripeSubscription.latest_invoice as Stripe.Invoice
      if (latestInvoice.paid) {
        updateData['stripe_subscription_status'] = 'active'
        updateData['status'] = USER_MEMBERSHIP_STATUS_ENUM.accepted
      } else {
        if (stripeSubscription['latest_invoice']['payment_intent']['status'] === 'requires_action') {
          return status200(res, {
            status: 'requires_action',
            stripe_subscription: stripeSubscription,
          })
        }
        return status400(res, `Your subscription was not succeed`)
      }

      updateData['stripe_subscription_id'] = stripeSubscription.id
      const result = await prisma.userMembership.update({
        where: { id: userMembership.id },
        data: updateData,
      })
      return res.status(200).send({ success: true, data: result })
    } catch (error) {
      console.error(error)
      Sentry.captureException(error)
      return status500(res, error.message)
    }
  })

export default handler
