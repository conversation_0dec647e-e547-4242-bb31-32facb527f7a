import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { deleteReactionById } from '@memberup/shared/src/libs/prisma/reaction'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).delete(async (req, res) => {
  try {
    const { id, category, operation_type: operationType, awarded_user_id: awardedUserId } = req.query
    const user = req['user']

    const result = await deleteReactionById(id as string, operationType as string, user, awardedUserId as string)
    if (result?.id) {
      return res.status(200).send({ success: true, data: result })
    } else if (category) {
      return res.status(200).send({ success: true })
    }
    res.status(400).json(errorHandler(result, 'Message'))
  } catch (err: any) {
    console.log('reaction err ===', err)
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'Message'))
  }
})

export default handler
