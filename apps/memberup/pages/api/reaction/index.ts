import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { parseQuery } from '@memberup/shared/src/libs/api-utils'
import { knockTriggerWorkflow } from '@memberup/shared/src/libs/knock'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { createReaction, findReactions } from '@memberup/shared/src/libs/prisma/reaction'
import { KNOCK_WORKFLOW_ENUM } from '@memberup/shared/src/types/enum'
import { refineStreamMessageInputText } from '@/memberup/libs/getstream'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const { where, take, select, skip, orderBy } = parseQuery(req.query)
      const result = await findReactions({
        where: { ...((where as any) || {}) },
        take,
        select,
        skip,
        orderBy,
      })
      res.json({ success: true, data: result })
    } catch (err: any) {
      console.log('getReactionsError =====', err)
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'Post'))
    }
  })
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { message_id, type, text, mentioned_users, permalink, all_members_ids, awarded_user_id } = req.body
      const mentionedUsersIds = mentioned_users?.map((user) => user.id)
      const payload = {
        data: {
          type,
          text: refineStreamMessageInputText(text),
          message: {
            connect: {
              id: message_id,
            },
          },
          user: {
            connect: {
              id: user.id,
            },
          },
        },
      }

      payload['data']['metadata'] = {
        mentioned_users: mentioned_users,
      }

      const result = await createReaction(payload, user, awarded_user_id)

      if (type !== 'like') {
        await knockTriggerWorkflow(
          KNOCK_WORKFLOW_ENUM.new_mention_notification,
          all_members_ids?.length ? all_members_ids : mentionedUsersIds,
          {
            url: permalink,
          },
          user.membership_id,
          user.id,
        )
      }

      if (result?.id) {
        res.status(201).send({
          success: true,
        })
      } else {
        res.status(500).json(errorHandler(result, 'Message'))
      }
    } catch (err: any) {
      console.log('reaction err ===', err)
      sentryCaptureException(err)
      res.status(500).json(errorHandler(err, 'Message'))
    }
  })

export default handler
