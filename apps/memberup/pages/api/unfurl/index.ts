import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { getUrlData } from '@memberup/shared/src/libs/urls'

const CACHE_RESULT_SECONDS = 60 * 60 * 24 // 1 day

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.post(async (req, res) => {
  try {
    const url = req.body.url

    if (!url || typeof url !== 'string') {
      return res.status(400).json({ error: 'A valid URL string is required' })
    }

    const data = await getUrlData(url)

    res.setHeader('Cache-Control', `public, max-age=${CACHE_RESULT_SECONDS}`).json({
      success: true,
      data,
    })
  } catch (err: any) {
    res.status(500).json({ error: 'Cannot process the request.' })
  }
})

export default handler
