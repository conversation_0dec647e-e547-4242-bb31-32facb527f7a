import { subDays } from 'date-fns'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import { useState } from 'react'

import { formatDate, getDatesInRange } from '@memberup/shared/src/libs/date-utils'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import { stripeGetPayments, stripeGetSubscriptions } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const { gte, lte, status } = req.body

      const user = req['user']
      const membershipSetting = user.current_user_membership.membership_setting
      if (
        !membershipSetting?.stripe_connect_account?.access_token &&
        !membershipSetting?.stripe_connect_account?.enabled
      ) {
        return res.json({
          success: true,
          data: {
            lastValue: 0,
            currentValue: 0,
            docs: [],
          },
        })
      }

      const connectedStripeAccountInfo = membershipSetting?.stripe_connect_account

      // const sixtyDaysAgo = Math.floor((Date.now() - (60 * 24 * 60 * 60 * 1000)) / 1000);
      // const thirtyDaysAgo = Math.floor((Date.now() - (30 * 24 * 60 * 60 * 1000)) / 1000);

      const endDate = new Date().setHours(23, 59, 59)
      let startDate = subDays(new Date().setHours(0, 0, 0), 29).getTime()
      let startDateBefore = subDays(new Date().setHours(0, 0, 0), 60).getTime()
      const last30days = getDatesInRange(startDate, endDate, 'yyyy/MM/dd')
      const dailySums = last30days.reduce((acc, d) => {
        acc[d] = 0
        return acc
      }, {})

      const payments = await stripeGetPayments(connectedStripeAccountInfo, {
        created: { gte: Math.round(startDateBefore / 1000) },
      })

      const paymentsCurrentMonth = payments.filter(
        (p) => p.created >= Math.round(startDate / 1000) && p.status === 'succeeded',
      )
      const paymentsLastMonth = payments.filter(
        (p) => p.created < Math.round(startDate / 1000) && p.status === 'succeeded',
      )

      const groupPaymentsByDay = (payments, dailySums) => {
        payments.forEach((payment) => {
          if (payment.status === 'succeeded') {
            const date = formatDate({
              date: new Date(payment.created * 1000).toISOString().split('T')[0],
              format: 'yyyy/MM/dd',
            })
            if (Object.keys(dailySums).includes(date)) {
              dailySums[date] += payment.amount_received
            }
          }
        })
        return Object.keys(dailySums).map((date) => ({
          date,
          value: parseFloat((dailySums[date] / 100).toFixed(2)),
        }))
      }
      const lastValue = parseFloat((paymentsLastMonth.reduce((sum, p) => sum + p.amount_received, 0) / 100).toFixed(2))
      const currentValue = parseFloat(
        (paymentsCurrentMonth.reduce((sum, p) => sum + p.amount_received, 0) / 100).toFixed(2),
      )
      const data = groupPaymentsByDay(paymentsCurrentMonth, dailySums)

      return res.json({
        success: true,
        data: {
          lastValue,
          currentValue,
          data: data,
          paymentsCurrentMonth,
          paymentsLastMonth,
        },
      })
    } catch (err) {
      res.status(500).json(errorHandler(err, 'User'))
    }
  })

export default handler
