import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorH<PERSON><PERSON> } from '@memberup/shared/src/libs/prisma/error-handler'
import { findNumOfUsers, findUsers } from '@memberup/shared/src/libs/prisma/user'
import { USER_ROLE_ENUM, USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .use(checkCreatorRoleMiddleware)
  .post(async (req, res) => {
    try {
      const { gte, lte, isNew, isCancel, isTotal } = req.body

      const user = req['user']

      if (isNew || isCancel) {
        const lastValue = await findNumOfUsers({
          where: {
            membership_id: req['user'].membership_id,
            role: USER_ROLE_ENUM.member,
            status: isNew ? USER_STATUS_ENUM.active : { in: [USER_STATUS_ENUM.banned, USER_STATUS_ENUM.deleted] },
            createdAt: {
              gte: new Date(2 * gte - lte - 1000 * 3600 * 24),
              lte: new Date(gte - 1000 * 3600 * 24),
            },
          },
        })

        const result = await findUsers({
          select: {
            createdAt: true,
            id: true,
          },
          where: {
            membership_id: req['user'].membership_id,
            role: USER_ROLE_ENUM.member,
            status: isNew ? USER_STATUS_ENUM.active : { in: [USER_STATUS_ENUM.banned, USER_STATUS_ENUM.deleted] },
            createdAt: {
              gte: new Date(gte),
              lte: new Date(lte),
            },
          },
        })

        return res.json({
          success: true,
          data: {
            lastValue,
            currentValue: result.total,
            docs: result.docs,
          },
        })
      }

      if (isTotal) {
        const result = await findUsers({
          select: {
            id: true,
          },
          where: {
            membership_id: req['user'].membership_id,
            status: USER_STATUS_ENUM.active,
          },
        })

        return res.json({
          success: true,
          data: {
            lastValue: 0,
            currentValue: result.total,
            docs: result.docs,
          },
        })
      }

      res.json({
        success: true,
        data: {
          lastValue: 0,
          currentValue: 0,
          docs: [],
        },
      })
    } catch (err) {
      res.status(400).json(errorHandler(err, 'User'))
    }
  })

export default handler
