import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findMembership } from '@memberup/shared/src/libs/prisma/membership'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.get(async (req, res) => {
  try {
    const { slug, name } = req.query as { slug: string; name: string }

    if (['university', 'auth'].indexOf(slug as string) >= 0) {
      return res.json({
        success: true,
      })
    }

    const result = await findMembership({
      where: { slug, name },
    })
    return res.json({
      success: <PERSON><PERSON><PERSON>(result?.id),
    })
  } catch (err: any) {
    res.status(400).json(errorHandler(err, 'Membership'))
  }
})

export default handler
