import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import { comparePassword } from '@memberup/shared/src/libs/bcrypt'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findMembership, updateMembership } from '@memberup/shared/src/libs/prisma/membership'
import { findUser } from '@memberup/shared/src/libs/prisma/user'
import { IMembership, IUser } from '@memberup/shared/src/types/interfaces'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import prisma from '@/shared-libs/prisma/prisma'
import { stripeCancelSubscription, stripeCancelSubscriptionMain } from '@/shared-libs/stripe'
import { USER_ROLE_ENUM } from '@/shared-types/enum'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { deactive_reason, password } = req.body

    // Get the membership, verify if the user is the owner
    const { membership_id: membershipId } = req.query

    if (!membershipId) {
      return res.status(400).send({
        success: false,
        message: 'membership_id is required.',
      })
    }

    // Check that only owners can deactivate a community account.
    const userMembership = await prisma.userMembership.findUnique({
      where: {
        user_id_membership_id: {
          user_id: user.id,
          membership_id: membershipId as string,
        },
        user_role: USER_ROLE_ENUM.owner,
      },
      include: {
        user: {
          include: {
            profile: true,
          },
        },
        membership: {
          include: {
            membership_setting: true,
          },
        },
      },
    })

    if (!userMembership) {
      return res.status(403).send({
        success: false,
        message: 'user is not the owner of the community.',
      })
    }

    const checkPassword = await comparePassword(password, userMembership.user.password)
    if (!checkPassword) {
      return res.status(200).send({
        success: false,
        reason: {
          password: 'Incorrect Password',
        },
      })
    }

    const membership = userMembership.membership
    const connectedStripeAccountInfo: any = membership?.membership_setting?.stripe_connect_account
    if (connectedStripeAccountInfo && userMembership.user.profile?.stripe_subscription_id) {
      await stripeCancelSubscription(
        connectedStripeAccountInfo,
        userMembership.user.profile?.stripe_subscription_id,
        false,
      )
    }

    if (membership?.membership_setting?.stripe_subscription_id) {
      await stripeCancelSubscriptionMain(STRIPE_SECRET_KEY, membership.membership_setting.stripe_subscription_id)
    }

    // const streamChannelIds = (membership?.channels || [])
    //   .map((c) => `${CHANNEL_TYPE_ENUM.team}:${c.id}`)
    //   .concat((membership?.libraries || []).map((c) => `${CHANNEL_TYPE_ENUM.team}:${c.id}`))
    //   .concat((membership?.lives || []).map((c) => `${CHANNEL_TYPE_ENUM.team}:${c.id}`))

    // if (streamChannelIds.length) {
    //   await deleteStreamChannels(streamChannelIds)
    // }

    // await deleteSparkMembershipCategories({
    //   where: { membership_id: user.membership_id },
    // })
    // await deleteMembershipById(user.membership_id)
    await updateMembership({
      where: { id: membership.id },
      data: { active: false, deactive_reason },
    })

    return res.status(200).send({
      success: true,
    })
  } catch (err: any) {
    console.log('cancelMembershipError ====', err)
    res.status(400).json(errorHandler(err, 'Membership'))
  }
})

export default handler
