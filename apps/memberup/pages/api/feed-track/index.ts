import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { getFeedTracksByUserId, upsertFeedTrack } from '@memberup/shared/src/libs/prisma/feed'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const result = await getFeedTracksByUserId(user.id)
      /* build  an object where the key is the feed_id */
      const feedTracksMap = result.reduce((acc, cur) => {
        acc[cur.feed_id] = Math.floor(new Date(cur.updatedAt).getTime() / 1000)
        return acc
      }, {})
      res.json({ success: true, data: feedTracksMap })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'Feed track'))
    }
  })
  .post(async (req, res) => {
    try {
      const user = req['user']
      const {
        body: { feed_id: feedId, updated_at: updatedAt },
      } = req

      await upsertFeedTrack(user.id, feedId, updatedAt)

      return res.status(200).send({ success: true })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'Feed track'))
    }
  })

export default handler
