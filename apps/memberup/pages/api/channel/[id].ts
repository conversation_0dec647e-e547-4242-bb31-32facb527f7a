import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { deleteChannel, findChannelById } from '@memberup/shared/src/libs/prisma/channel'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { updateChannel } from '@/shared-libs/prisma/channel'
import { findUserMembershipAsAdminOrCreator } from '@/shared-libs/prisma/user-membership'

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .get(async (req, res) => {
    try {
      const { id } = req.query
      const result = await findChannelById(id as string)

      if (result?.id) {
        res.json({ success: true, data: result })
      } else {
        res.status(400).json(errorHandler(result, 'Space'))
      }
    } catch (err) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'Space'))
    }
  })
  .put(async (req, res) => {
    try {
      const user = req['user']
      const { id } = req.query
      const { ...rest } = req.body

      // Verify if the channel exists
      const channel = await findChannelById(id as string)
      if (!channel) {
        return res.status(400).json({ message: `Channel not found.` })
      }

      // Verify if the user has the permission to update the channel
      const userMembership = await findUserMembershipAsAdminOrCreator(user.id, channel.membership_id)
      if (!userMembership) {
        return res.status(403).json({ message: `You don't have permissions to update the channel.` })
      }

      // TODO: Check this as it is updating all channels, not just the ones in the same membership
      // if (typeof rest?.sequence === 'number') {
      //     const oldSequence = channel.sequence
      //     if (rest.sequence > oldSequence) {
      //         await updateChannels({
      //             where: {
      //                 sequence: {gt: oldSequence, lte: rest.sequence},
      //             },
      //             data: {
      //                 sequence: {decrement: 1},
      //             },
      //         })
      //     } else if (rest.sequence < oldSequence) {
      //         await updateChannels({
      //             where: {
      //                 sequence: {gte: rest.sequence, lt: oldSequence},
      //             },
      //             data: {
      //                 sequence: {increment: 1},
      //             },
      //         })
      //     }
      // }
      const updatedChannel = await updateChannel({
        where: { id: channel.id },
        data: { ...rest, created_by_id: user.id },
      })
      if (updatedChannel?.id) {
        return res.status(200).send({ success: true, data: updatedChannel })
      } else {
        return res.status(400).json(errorHandler(updatedChannel, 'Space'))
      }
    } catch (err: any) {
      return res.status(500).json(errorHandler(err, 'Space'))
    }
  })
  .delete(async (req, res) => {
    try {
      const user = req['user']
      const { id } = req.query

      // Verify if the channel exists
      const channel = await findChannelById(id as string)
      if (!channel) {
        return res.status(400).json({ message: `Channel not found.` })
      }

      // Verify if the user has the permission to delete the channel
      const userMembership = await findUserMembershipAsAdminOrCreator(user.id, channel.membership_id)
      if (!userMembership) {
        return res.status(403).json({ message: `You don't have permissions to delete the channel.` })
      }
      const result = await deleteChannel(id as string)
      if (result?.id) {
        res.status(200).send({ success: true, data: result })
      } else {
        return res.status(400).json(errorHandler(result, 'Space'))
      }
    } catch (err: any) {
      return res.status(500).json(errorHandler(err, 'Space'))
    }
  })

export default handler
