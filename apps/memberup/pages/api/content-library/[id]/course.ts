import { LIBRARY_VISIBILITY_ENUM } from '@prisma/client'
import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { roleCanManageCommunity } from '@memberup/shared/src/libs/authorization'
import { findContentLibraryById } from '@memberup/shared/src/libs/prisma/content-library'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { prisma } from '@/shared-libs/prisma/prisma'
import { findUserMembership, findUserMemberships } from '@/shared-libs/prisma/user-membership'
import { MEMBERUP_PLAN_ENUM, USER_MEMBERSHIP_STATUS_ENUM } from '@/shared-types/enum'

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    const user = req['user']
    const { id: content_library_id } = req.query
    const contentLibrary = await findContentLibraryById(content_library_id as string)

    const userMembership = await findUserMembership({
      where: {
        user_id: user.id,
        membership: {
          id: contentLibrary.membership_id,
        },
      },
    })
    if (!userMembership) {
      return res.status(400).json({ error: 'User is not a member of this community' })
    }

    const visibility = roleCanManageCommunity(userMembership?.user_role)
      ? {
          not: 'deleted',
        }
      : {
          not: {
            in: ['deleted', 'draft'],
          },
        }

    try {
      // Get all the user memberships that the user is owner and check for any annual plan.
      let userMembershipsAsOwner: any = await findUserMemberships({
        where: {
          status: USER_MEMBERSHIP_STATUS_ENUM.accepted,
          user_role: 'owner',
        },
        include: {
          membership: {
            include: {
              membership_setting: true,
            },
          },
        },
      })

      let hasAnnualPlan = false
      for (const userMembership of userMembershipsAsOwner) {
        if (userMembership.membership.membership_setting.plan === MEMBERUP_PLAN_ENUM.enterprise_annual) {
          hasAnnualPlan = true
          break
        }
      }

      const libraryCourses = await prisma.contentLibraryCourse.findMany({
        where: {
          content_library_id: content_library_id as string,
          visibility: visibility as any,
        },
        include: {
          ContentLibraryCourseSection: {
            include: {
              ContentLibraryCourseLesson: {
                orderBy: {
                  sequence: 'asc',
                },
              },
            },
            orderBy: {
              sequence: 'asc',
            },
          },
          ContentLibraryCourseUserProgress: {
            where: {
              user_id: user.id,
            },
          },
          contentLibrary: {
            select: {
              metadata: true,
            },
          },
        },
      })

      let filteredLibraryCourses = libraryCourses
      if (!hasAnnualPlan) {
        filteredLibraryCourses = libraryCourses.filter((l) => !l.only_for_annual_plan)
      }

      res.status(200).json({
        success: true,
        data: {
          library_courses: filteredLibraryCourses,
        },
      })
    } catch (err: any) {
      console.error(err)
      Sentry.captureException(err)
      res.status(400).json(errorHandler(err, 'ContentLibraryCourse'))
    }
  })
  .post(async (req, res) => {
    try {
      const { id: content_library_id } = req.query
      const { title, description, thumbnail } = req.body

      const contentLibrary = await findContentLibraryById(content_library_id as string)

      if (!contentLibrary) {
        return res.status(400).json({ error: 'ContentLibrary not found' })
      }

      const membershipId = contentLibrary.membership_id

      if (!title) {
        return res.status(400).json({ error: 'Missing required fields' })
      }

      const newCourse = await prisma.contentLibraryCourse.create({
        data: {
          title,
          description,
          visibility: 'draft' as LIBRARY_VISIBILITY_ENUM,
          thumbnail,
          membership: { connect: { id: membershipId } },
          contentLibrary: { connect: { id: content_library_id as string } },
        },
      })

      // Create a default section
      const newSection = await prisma.contentLibraryCourseSection.create({
        data: {
          name: 'Default Section',
          membership: { connect: { id: membershipId } },
          ContentLibraryCourse: { connect: { id: newCourse.id } },
          ContentLibrary: { connect: { id: content_library_id as string } },
        },
      })

      // Create a default lesson
      await prisma.contentLibraryCourseLesson.create({
        data: {
          title: 'Default Lesson',
          visibility: 'draft' as LIBRARY_VISIBILITY_ENUM,
          membership: { connect: { id: membershipId } },
          section: { connect: { id: newSection.id } },
          ContentLibrary: { connect: { id: content_library_id as string } },
          ContentLibraryCourse: { connect: { id: newCourse.id } },
        },
      })

      if (contentLibrary && contentLibrary.metadata) {
        // If course_order doesn't exist, initialize it as an empty array
        if (!(contentLibrary.metadata as any).course_order) {
          ;(contentLibrary.metadata as any).course_order = []
        }

        ;(contentLibrary.metadata as any).course_order.push(newCourse.id)

        // Update the ContentLibrary record
        await prisma.contentLibrary.update({
          where: { id: content_library_id as string },
          data: { metadata: contentLibrary.metadata },
        })
      }

      res.status(200).json(newCourse)
    } catch (err: any) {
      console.error(err)
      Sentry.captureException(err)
      res.status(400).json(errorHandler(err, 'ContentLibraryCourse'))
    }
  })
  .put(async (req, res) => {
    try {
      const { id: user_id } = req['user']
      const { id: content_library_id, course_id } = req.query
      const { title, description, thumbnail, visibility } = req.body

      const dataToUpdate: any = {}
      if (title) dataToUpdate.title = title
      if (description) dataToUpdate.description = description
      if (thumbnail) dataToUpdate.thumbnail = thumbnail
      if (visibility) dataToUpdate.visibility = visibility as LIBRARY_VISIBILITY_ENUM

      const updatedCourse = await prisma.contentLibraryCourse.update({
        where: {
          id: course_id as string,
        },
        data: {
          ...dataToUpdate,
          contentLibrary: { connect: { id: content_library_id as string } },
        },
      })

      res.status(200).json(updatedCourse)
    } catch (err: any) {
      console.error(err)
      Sentry.captureException(err)
      res.status(400).json(errorHandler(err, 'ContentLibraryCourse'))
    }
  })

export default handler
