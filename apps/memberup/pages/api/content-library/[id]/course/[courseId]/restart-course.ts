import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { prisma } from '@/shared-libs/prisma/prisma'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).delete(async (req, res) => {
  try {
    const user = req['user']
    const { courseId, id: contentLibraryId, membership_id } = req.query
    const membershipId = membership_id as string

    await prisma.contentLibraryCourseUserProgress.update({
      where: {
        user_id_membership_id_content_library_course_id_content_library_id: {
          user_id: user.id,
          membership_id: membershipId,
          content_library_course_id: courseId as string,
          content_library_id: contentLibraryId as string,
        },
      },
      data: {
        done: {},
        progress_percentage: 0,
      },
    })

    res.status(200).end('Course restarted')
  } catch (err: any) {
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'UserLessonStatus'))
  }
})

export default handler
