import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { prisma } from '@/shared-libs/prisma/prisma'
import { findUserMembership } from '@/shared-libs/prisma/user-membership'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { id: contentLibraryId, courseId, sectionId, membership_id } = req.query
    const { title, type, text, media_file, resource_files, visibility } = req.body

    const membershipId = membership_id as string

    const userMembership = await findUserMembership({
      where: {
        user_id: user.id,
        membership_id: membershipId,
      },
    })

    if (!userMembership) {
      return res.status(400).json({ message: 'You are not a member of this membership' })
    }

    // Fetch the last lesson to get the sequence
    const lastLesson = await prisma.contentLibraryCourseLesson.findFirst({
      where: { section_id: sectionId as string },
      orderBy: { sequence: 'desc' },
    })

    const sequence = lastLesson ? lastLesson.sequence + 1 : 1
    const newLesson = await prisma.contentLibraryCourseLesson.create({
      data: {
        visibility,
        title,
        type,
        text,
        media_file,
        resource_files,
        sequence,
        section_id: sectionId as string,
        membership_id: membershipId,
        content_library_id: contentLibraryId as string,
        content_library_course_id: courseId as string,
      },
    })

    res.status(200).json(newLesson)
  } catch (err: any) {
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'ContentLibraryCourseLesson'))
  }
})

export default handler
