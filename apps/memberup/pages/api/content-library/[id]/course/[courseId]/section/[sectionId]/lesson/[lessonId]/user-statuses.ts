import * as Sentry from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { findContentLibraryById } from '@memberup/shared/src/libs/prisma/content-library'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { createActionHistory, deleteActionHistory } from '@/shared-libs/prisma/actions'
import { prisma } from '@/shared-libs/prisma/prisma'

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']
      const { id: contentLibraryId, courseId } = req.query

      const contentLibrary = await findContentLibraryById(contentLibraryId as string)

      console.log(contentLibrary)
      const userLessonStatus = await prisma.contentLibraryCourseUserProgress.findUnique({
        where: {
          user_id_membership_id_content_library_course_id_content_library_id: {
            user_id: user.id,
            membership_id: contentLibrary.membership_id,
            content_library_course_id: courseId as string,
            content_library_id: contentLibraryId as string,
          },
        },
      })

      res.status(200).json(userLessonStatus)
    } catch (err: any) {
      console.log(err)
      Sentry.captureException(err)
      res.status(400).json(errorHandler(err, 'UserLessonStatus'))
    }
  })
  .put(async (req, res) => {
    try {
      const user = req['user']
      const { id: contentLibraryId, courseId } = req.query
      const lessonId = req.query.lessonId as string
      const { done } = req.body

      const contentLibrary = await findContentLibraryById(contentLibraryId as string)

      console.log(contentLibrary)

      const userLessonStatus = await prisma.contentLibraryCourseUserProgress.findUnique({
        where: {
          user_id_membership_id_content_library_course_id_content_library_id: {
            user_id: user.id,
            membership_id: contentLibrary.membership_id,
            content_library_course_id: courseId as string,
            content_library_id: contentLibraryId as string,
          },
        },
      })

      let updatedDoneField = userLessonStatus ? { ...(userLessonStatus.done as any) } : {}

      if (done === false) {
        // If done is false, delete the record
        delete updatedDoneField[lessonId]
      } else {
        // If done is not false, upsert the record
        updatedDoneField[lessonId] = true
      }

      // Fetch the course data to get the total number of lessons
      const courseData = await prisma.contentLibraryCourse.findUnique({
        where: { id: courseId as string },
        include: { ContentLibraryCourseSection: { include: { ContentLibraryCourseLesson: true } } },
      })

      const totalLessons = courseData.ContentLibraryCourseSection.reduce((total, section) => {
        const publishedLessons = section.ContentLibraryCourseLesson.filter(
          (lesson) => lesson.visibility === 'published',
        )
        return total + publishedLessons.length
      }, 0)
      // Calculate the progress percentage with a reduce
      const completedLessons = courseData.ContentLibraryCourseSection.reduce((total, section) => {
        const publishedLessons = section.ContentLibraryCourseLesson.filter(
          (lesson) => lesson.visibility === 'published',
        )
        const completedLessonsInSection = publishedLessons.filter((lesson) => updatedDoneField[lesson.id])
        return total + completedLessonsInSection.length
      }, 0)
      console.log('completed', completedLessons, 'totalLessons', totalLessons)
      const progressPercentage = (completedLessons / totalLessons) * 100

      const newUserLessonStatus = await prisma.contentLibraryCourseUserProgress.upsert({
        where: {
          user_id_membership_id_content_library_course_id_content_library_id: {
            user_id: user.id,
            membership_id: contentLibrary.membership_id,
            content_library_course_id: courseId as string,
            content_library_id: contentLibraryId as string,
          },
        },
        update: {
          done: updatedDoneField,
          progress_percentage: progressPercentage,
        },
        create: {
          user_id: user.id,
          membership_id: contentLibrary.membership_id,
          done: updatedDoneField,
          content_library_course_id: courseId as string,
          content_library_id: contentLibraryId as string,
          progress_percentage: progressPercentage,
        },
      })

      const action = await prisma.action.findFirst({
        where: { action_name: 'LESSON_COMPLETED' },
      })

      if (done === false) {
        await deleteActionHistory(user.id, action, lessonId)
      } else {
        await createActionHistory(user.id, action, lessonId)
      }

      res.status(200).json(newUserLessonStatus)
    } catch (err: any) {
      console.log(err)
      Sentry.captureException(err)
      res.status(400).json(errorHandler(err, 'UserLessonStatus'))
    }
  })
export default handler
