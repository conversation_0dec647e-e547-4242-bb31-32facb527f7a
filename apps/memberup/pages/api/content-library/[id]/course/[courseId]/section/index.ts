// pages/api/contentLibrary/[id]/section/index.tsx
import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHand<PERSON> } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { prisma } from '@/shared-libs/prisma/prisma'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { id: contentLibraryId, membership_id } = req.query
    const { name, content_library_course_id } = req.body
    const membershipId = membership_id as string

    // Fetch the last section to get the sequence
    const lastSection = await prisma.contentLibraryCourseSection.findFirst({
      where: { content_library_course_id },
      orderBy: { sequence: 'desc' },
    })

    const sequence = lastSection ? lastSection.sequence + 1 : 1

    const newSection = await prisma.contentLibraryCourseSection.create({
      data: {
        name,
        sequence,
        content_library_course_id,
        membership_id: membershipId,
        content_library_id: contentLibraryId as string,
      },
    })

    res.status(200).json(newSection)
  } catch (err: any) {
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'ContentLibraryCourseSection'))
  }
})

export default handler
