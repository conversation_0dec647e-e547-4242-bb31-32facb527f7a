import { captureException as sentryCaptureException } from '@sentry/nextjs'
import merge from 'lodash/merge'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { prisma } from '@/shared-libs/prisma/prisma'
import { LibraryMetadata } from '@/shared-types/types'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const { id } = req.query
      const contentLibrary = await prisma.contentLibrary.findUnique({
        where: { id: id as string },
        include: {
          ContentLibraryCourse: true,
        },
      })

      if (!contentLibrary) {
        return res.status(404).json({ error: 'ContentLibrary not found' })
      }
      const contentLibraryMetadata = contentLibrary.metadata as LibraryMetadata
      if (contentLibraryMetadata.course_order) {
        const courseOrder = contentLibraryMetadata.course_order
        contentLibrary.ContentLibraryCourse.sort((a, b) => courseOrder.indexOf(a.id) - courseOrder.indexOf(b.id))
      }

      res.status(200).json({ success: true, data: contentLibrary })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'ContentLibrary'))
    }
  })
  .put(async (req, res) => {
    try {
      const user = req['user']
      const { id, membership_id } = req.query
      const { id: _, ...updateData } = req.body
      const membershipId = membership_id as string

      const contentLibrary = await prisma.contentLibrary.findUnique({
        where: { id: id as string, membership_id: membershipId },
      })

      if (!contentLibrary) {
        return res.status(404).json({ error: 'ContentLibrary not found' })
      }

      // Merge incoming metadata with current metadata
      const mergedMetadata = merge({}, contentLibrary.metadata, updateData.metadata)

      const updatedContentLibrary = await prisma.contentLibrary.update({
        where: { id: id as string },
        data: {
          ...updateData,
          metadata: mergedMetadata,
        },
      })

      res.status(200).json({ success: true, data: updatedContentLibrary })
    } catch (err: any) {
      sentryCaptureException(err)
      res.status(400).json(errorHandler(err, 'ContentLibrary'))
    }
  })

export default handler
