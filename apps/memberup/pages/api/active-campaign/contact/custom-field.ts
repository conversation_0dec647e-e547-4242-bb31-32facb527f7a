import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import {
  activeCampaignCreateContactCustomFieldValue,
  activeCampaignGetContactFieldValues,
  activeCampaignGetContacts,
  activeCampaignGetCustomFields,
  activeCampaignUpdateContactCustomFieldValue,
} from '@memberup/shared/src/libs/active-campaign'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const ACTIVE_CAMPAIGN_API_URL = process.env.ACTIVE_CAMPAIGN_API_URL
const ACTIVE_CAMPAIGN_API_KEY = process.env.ACTIVE_CAMPAIGN_API_KEY

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { data } = req.body
    const ACSetting = user.membership_setting.active_campaign
    const apiUrl = ACSetting.api_url || ACTIVE_CAMPAIGN_API_URL
    const apiKey = ACSetting.api_key || ACTIVE_CAMPAIGN_API_KEY
    const ACContact = await activeCampaignGetContacts(apiUrl, apiKey, user.email)

    if (ACContact?.contacts?.[0]) {
      const promises = []
      const contactId = ACContact.contacts[0].id
      const contactFieldValues = await activeCampaignGetContactFieldValues(apiUrl, apiKey, contactId)
      const customFields = await activeCampaignGetCustomFields(apiUrl, apiKey)
      for (const d of data) {
        const customField = customFields.fields.find((f) => f.title === d.field)
        if (customField) {
          const option = customFields.fieldOptions.find(
            (fo) => fo.field === customField.id && fo.value.trim() === d.value.trim(),
          )
          if (option?.id) {
            const contactFieldValue = contactFieldValues.fieldValues.find((fv) => fv.field === customField.id)

            try {
              if (contactFieldValue) {
                await activeCampaignUpdateContactCustomFieldValue(
                  apiUrl,
                  apiKey,
                  contactId,
                  customField.id,
                  contactFieldValue.id,
                  option.value,
                )
              } else {
                await activeCampaignCreateContactCustomFieldValue(
                  apiUrl,
                  apiKey,
                  contactId,
                  customField.id,
                  option.value,
                )
              }
            } catch (err) {}
          }
        }
      }
      return res.json({ success: true })
    }
    return res.json({ success: false })
  } catch (err: any) {
    console.error(err)
    return res.json({ success: false })
  }
})

export default handler
