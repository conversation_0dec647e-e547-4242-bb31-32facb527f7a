import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.post(async (req, res) => {
  try {
    const {
      channel_id,
      channel_type,
      cid,
      created_at,
      message,
      members,
      reaction,
      received_at,
      total_unread_count,
      type,
      user,
      watcher_count,
    } = req.body

    switch (type) {
      case 'channel.created':
        break
      case 'channel.deleted':
        break
      case 'channel.hidden':
        break
      case 'channel.truncated':
        break
      case 'channel.updated':
        break
      case 'member.added':
        break
      case 'member.removed':
        break
      case 'member.updated':
        break
      case 'message.deleted':
        break
      case 'message.new':
        break
      case 'message.read':
        break
      case 'message.updated':
        break
      case 'reaction.deleted':
        break
      case 'reaction.new':
        break
      case 'reaction.updated':
        break
      case 'user.banned':
        break
      case 'user.deleted':
        break
      case 'user.unbanned':
        break
      case 'user.updated':
        break
    }
    res.json({ success: true })
  } catch (err: any) {
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'Space'))
  }
})

export default handler
