import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import Stripe from 'stripe'

import { STRIPE_SECRET_KEY, STRIPE_WEBHOOK_MEMBERSHIP_SECRET } from '@memberup/shared/src/config/envs'
import { StripeEventEnum } from '@memberup/shared/src/libs/stripe'
import { stripeWebhook } from '@memberup/shared/src/libs/stripe-webhook'
import { inngest } from '@memberup/shared/src/services/inngest'

export const config = {
  api: {
    bodyParser: false,
  },
}

const handler = nc<NextApiRequest, NextApiResponse>({})

handler.post(async (req, res) => {
  try {
    let event: Stripe.Event
    event = await stripeWebhook(
      STRIPE_SECRET_KEY,
      // membershipSetting.stripe_connect_account.access_token,
      STRIPE_WEBHOOK_MEMBERSHIP_SECRET,
      req,
    )

    // Extract the object from the event.
    const dataObject = event?.data?.object

    // Handle the event
    // Review important events for Billing webhooks
    // https://stripe.com/docs/billing/webhooks
    // Remove comment to see the various objects sent for this sample
    switch (event?.type) {
      case StripeEventEnum.CUSTOMER_DELETED:
        await inngest.send({
          name: 'stripe/customer.deleted',
          data: { ...dataObject, isMembershipStripe: true },
        })
        break
      case StripeEventEnum.CUSTOMER_SUBSCRIPTION_TRIAL_WILL_END:
        break
      case StripeEventEnum.CUSTOMER_SUBSCRIPTION_CREATED:
      case StripeEventEnum.CUSTOMER_SUBSCRIPTION_UPDATED:
        // Then define and call a function to handle the event customer.subscription.updated
        if (dataObject['status'] !== 'incomplete') {
          await inngest.send({
            name: 'stripe/customer.subscription.updated',
            data: { ...dataObject, isMembershipStripe: true },
          })
        }
        break
      case StripeEventEnum.CUSTOMER_SUBSCRIPTION_DELETED:
        await inngest.send({
          name: 'stripe/customer.subscription.deleted',
          data: { ...dataObject, isMembershipStripe: true },
        })
        break
      case StripeEventEnum.INVOICE_PAID:
        // Used to provision services after the trial has ended.
        // The status of the invoice will show up as paid. Store the status in your
        // database to reference when a user accesses your service to avoid hitting rate limits.
        await inngest.send({
          name: 'stripe/invoice.paid',
          data: { ...dataObject, isMembershipStripe: true },
        })
        break
      case StripeEventEnum.INVOICE_PAYMENT_FAILED:
        // If the payment fails or the customer does not have a valid payment method,
        //  an invoice.payment_failed event is sent, the subscription becomes past_due.
        // Use this webhook to notify your user that their payment has
        // failed and to retrieve new card details.
        break
      case StripeEventEnum.PAYMENT_METHOD_ATTACHED:
      case StripeEventEnum.PAYMENT_METHOD_UPDATED:
      case StripeEventEnum.PAYMENT_METHOD_AUTOMATICALLY_UPDATED:
        await inngest.send({
          name: 'stripe/payment_method.attached',
          data: { ...dataObject, isMembershipStripe: true },
        })
        break
      case StripeEventEnum.PAYMENT_INTENT_SUCCEEDED:
        await inngest.send({
          name: 'stripe/payment_intent.succeeded',
          data: { ...dataObject, isMembershipStripe: true },
        })
      default:
      // Unexpected event type
    }
    res.status(200).json({ success: true })
  } catch (err: any) {
    res.status(200).json({ success: false, error: err.message })
  }
})

export default handler
