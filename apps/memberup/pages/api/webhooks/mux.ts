import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { inngest } from '@memberup/shared/src/services/inngest'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.post(async (req, res) => {
  try {
    const { type, data } = req.body

    switch (type) {
      case 'video.asset.created':
      case 'video.asset.ready':
      case 'video.asset.static_renditions.ready':
        await inngest.send({
          name: 'mux/video.asset.status',
          data: {
            type,
            data,
          },
        })
    }

    return res.json({ success: true })
  } catch (err: any) {
    console.error(err)
  }
})

export default handler
