import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { parseQuery } from '@memberup/shared/src/libs/api-utils'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findNotifications } from '@memberup/shared/src/libs/prisma/notification'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const user = req['user']
    const { where, take, select, skip, orderBy } = parseQuery(req.query)
    const result = await findNotifications({
      where: { ...((where as any) || {}), user_id: user.id },
      take,
      select,
      skip,
      orderBy: [{ createdAt: 'desc' }],
    })
    res.json({ success: true, data: result })
  } catch (err: any) {
    console.log('err ====', err)
    // sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'Space'))
  }
})

export default handler
