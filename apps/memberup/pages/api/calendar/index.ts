import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { parseQuery } from '@memberup/shared/src/libs/api-utils'
import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findFeeds } from '@memberup/shared/src/libs/prisma/feed'
import { findLives } from '@memberup/shared/src/libs/prisma/live'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const { where, take, select, skip, orderBy } = parseQuery(req.query)
    const lives = await findLives({ where, take, select, skip, orderBy })
    const posts = await findFeeds({ where, take, select, skip, orderBy })
    res.json({
      success: true,
      data: {
        total: lives.total + posts.total,
        docs: [...lives.docs, ...posts.docs],
      },
    })
  } catch (err: any) {
    res.status(400).json(errorHandler(err, 'Calendar'))
  }
})

export default handler
