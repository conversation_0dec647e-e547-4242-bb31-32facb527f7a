import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { deleteStreamChannel } from '@memberup/shared/src/libs/stream-chat'
import authenticationMiddleware from '@/memberup/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).delete(async (req, res) => {
  try {
    const { id } = req.query
    const result = await deleteStreamChannel(id as string)
    if (result?.task_id) {
      res.status(200).send({ success: true, data: result })
    } else {
      res.status(400).json(errorHandler(result, 'StreamChannel'))
    }
  } catch (err: any) {
    sentryCaptureException(err)
    res.status(400).json(errorHandler(err, 'StreamChannel'))
  }
})

export default handler
