// Ensure Doppler is set up to run on the staging environment
// doppler run -- node  apps/memberup/scripts/generate-actions-history.js

require('dotenv').config()
const { PrismaClient, Prisma } = require('@prisma/client')
const { StreamChat } = require('stream-chat')
const { askQuestion, getStreamApps } = require('./common')

const prisma = new PrismaClient()

const DATABASE_URL = process.env.DATABASE_URL
const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET

const generateActionsHistory = async () => {
  console.log(`Using Database URL ${DATABASE_URL}`)
  console.log(`Using GetStream App: key=${GET_STREAM_APP_KEY}, name=${getStreamApps[GET_STREAM_APP_KEY].name}`)

  const answer = await askQuestion('Do you want to proceed? (y/n): ')

  if (answer.toLowerCase() !== 'y') {
    console.log('Good bye.')
    return
  }

  const likeAction = await prisma.action.findUnique({
    where: {
      action_name: 'LIKE',
    },
  })
  const postCreatedAction = await prisma.action.findUnique({
    where: {
      action_name: 'POST_CREATED',
    },
  })
  const commentCreatedAction = await prisma.action.findUnique({
    where: {
      action_name: 'COMMENT_CREATED',
    },
  })

  const createActionHistoryRecords = async (dataBatch) => {
    const actionsPerDayMap = new Map()
    const actionHistoryData = dataBatch.map((data) => ({
      action_id: data.action.id,
      user_id: data.user_id,
      createdAt: data.date,
      resource_id: data.resource_id !== undefined ? data.resource_id : null,
    }))

    dataBatch.forEach((data) => {
      const day = new Date(data.date.toISOString().substring(0, 10))
      const delta = data.action.action_name !== 'UNLIKE' ? 1 : -1
      const key = `${data.user_id}-${day.toISOString()}`

      if (!actionsPerDayMap.has(key)) {
        actionsPerDayMap.set(key, {
          day,
          count: delta,
          user_id: data.user_id,
          resource_id: data.resource_id,
        })
      } else {
        actionsPerDayMap.get(key).count += delta
      }
    })

    const actionsPerDayQueries = Array.from(actionsPerDayMap.values()).map((data) => {
      return prisma.actionsPerDay.upsert({
        where: {
          user_id_day: {
            user_id: data.user_id,
            day: data.day,
          },
        },
        create: {
          day: data.day,
          count: data.count > 0 ? data.count : 0,
          user_id: data.user_id,
        },
        update: {
          count: {
            increment: data.count,
          },
        },
      })
    })

    await prisma.$transaction([prisma.actionHistory.createMany({ data: actionHistoryData }), ...actionsPerDayQueries])
  }

  // Process likes
  const batchSize = 50
  let skip = 0
  const reactionsCountResult = await prisma.$queryRaw(
    Prisma.raw(
      `select count(*) as count from (select max(createdAt) as created_at, user_id, message_id from reactions where type = 'like' group by user_id, message_id) t`,
    ),
  )
  const reactionsCount = Number(reactionsCountResult[0]['count'])
  const totalBatches = Math.ceil(reactionsCount / batchSize)
  while (true) {
    const rawQuery = `select max(createdAt) as created_at, user_id, message_id from reactions where type = 'like' group by user_id, message_id order by created_at, user_id, message_id limit ${skip},${batchSize}`
    const reactions = await prisma.$queryRaw(Prisma.raw(rawQuery))
    if (!reactions || reactions.length === 0) {
      break // Exit the loop if no more records are found
    }

    const currentBatch = skip / batchSize + 1
    if (currentBatch > 1) {
      break
    }
    console.log(`Processing likes batch ${currentBatch} of ${totalBatches}`)
    const dataBatch = reactions.map((reaction) => ({
      action: likeAction,
      date: new Date(reaction.created_at),
      user_id: reaction.user_id,
      resource_id: reaction.message_id,
    }))

    try {
      await createActionHistoryRecords(dataBatch)
    } catch (e) {
      console.error(e)
      console.error(dataBatch)
    }
    skip += batchSize
  }

  console.log('Likes processed successfully')

  // Process user posts & comments
  const communityChannels = {}

  const getCommunityChannels = async (membershipId) => {
    if (communityChannels[membershipId]) return communityChannels[membershipId]

    const channels = await prisma.channel.findMany({
      where: {
        active: true,
        membership_id: membershipId,
      },
    })

    communityChannels[membershipId] = channels.map((channel) => channel.id)

    return communityChannels[membershipId]
  }

  const streamClient = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
    timeout: 30000,
  })

  const fetchStreamMessages = async (channels, filters, options) => {
    const channelFilters = { id: { $in: channels } }
    const messageFilters = {
      ...filters,
      deleted_at: { $exists: false },
      feed_status: { $nin: ['rejected', 'reported'] },
    }

    const searchResponse = await streamClient.search(channelFilters, messageFilters, {
      ...options,
      sort: { created_at: -1 },
      limit: batchSize,
    })

    return {
      next: searchResponse.next || 'EOF',
      results: searchResponse.results.map((message) => message.message),
    }
  }

  const processUserPosts = async (user, channels) => {
    let nextPage = null

    do {
      const { next, results } = await fetchStreamMessages(
        channels,
        { 'user.id': user.id, parent_id: { $exists: false } },
        nextPage !== null ? { next: nextPage } : {},
      )

      if (Array.isArray(results) && results.length > 0) {
        const dataBatch = results.map((post) => ({
          action: postCreatedAction,
          date: new Date(post.created_at),
          user_id: user.id,
          resource_id: post.id,
        }))

        console.log(`Creating ${dataBatch.length} action history records for user ${user.id} posts`)
        await createActionHistoryRecords(dataBatch)
      }

      if (next === 'EOF') {
        break
      }

      nextPage = next ? next : null
    } while (nextPage !== null)
  }

  const processUserComments = async (user, channels) => {
    let nextPage = null

    do {
      const { next, results } = await fetchStreamMessages(
        channels,
        { 'user.id': user.id, parent_id: { $exists: true } },
        nextPage !== null ? { next: nextPage } : {},
      )

      if (Array.isArray(results) && results.length > 0) {
        const dataBatch = results.map((comment) => ({
          action: commentCreatedAction,
          date: new Date(comment.created_at),
          user_id: user.id,
          resource_id: comment.id,
        }))

        console.log(`Creating ${dataBatch.length} action history records for user ${user.id} comments`)
        await createActionHistoryRecords(dataBatch)
      }

      if (next === 'EOF') {
        break
      }

      nextPage = next ? next : null
    } while (nextPage !== null)
  }

  const users = await prisma.user.findMany({
    where: {
      status: {
        not: 'deleted',
      },
      membership_id: 'a928fd24-3d7e-4ae7-aa27-3764377341f2',
    },
  })

  for (const user of users) {
    const channels = await getCommunityChannels(user.membership_id)

    if (Array.isArray(channels) && channels.length > 0) {
      await processUserPosts(user, channels)
      await processUserComments(user, channels)
    }
  }
  console.log('Posts & comments processed successfully\nAll Actions & ActionsPerDay records have been created')
}

generateActionsHistory()
