// Creates tenants in Knock for all communities or a specific community
// Add --yes parameter for unattended operation
//
// Ensure Doppler is set up to run on the staging environment
// doppler run -- env TS_NODE_PROJECT=packages/shared/tsconfig.json node apps/memberup/scripts/knock-add-tenants.js [Community Slugs]

import 'dotenv/config'

import { Knock } from '@knocklabs/node'
import { PrismaClient } from '@prisma/client'
import { hideBin } from 'yargs/helpers'
import yargs from 'yargs/yargs'

import { confirmScriptExecution } from './common.js'

const prisma = new PrismaClient()

const KNOCK_API_KEY = process.env.KNOCK_SECRET_API_KEY

const argv = yargs(hideBin(process.argv))
  .option('community-slugs', {
    type: 'string',
    description: 'Comma-separated list of community slugs',
    coerce: (arg) => (arg ? arg.split(',') : []),
  })
  .option('yes', {
    type: 'boolean',
    description: 'Unattended operation',
  }).argv

// Delay function
const delay = (ms) => new Promise((res) => setTimeout(res, ms))

const getMembershipsBySlugs = async (communitySlugs) => {
  console.log('Fetching membership from database...', communitySlugs)
  const memberships = await prisma.membership.findMany({
    where: {
      slug: {
        in: communitySlugs?.length ? communitySlugs : undefined,
      },
    },
    select: {
      id: true,
      slug: true,
      name: true,
    },
  })
  return memberships
}

const addKnockTenants = async (memberships) => {
  const knockClient = new Knock(KNOCK_API_KEY)

  let count = 0
  for (let membership of memberships) {
    count++
    try {
      console.log(`====== Add Tenant for ${membership.slug} community - ${count} of ${memberships.length} =======`)
      await knockClient.tenants.set(membership.id, { name: membership.name, slug: membership.slug }) // Max 60 requests per second
    } catch (err) {
      console.error('err ====', err)
      process.exit(1)
    }
  }
  console.log('====== All tenants have been created =======')
}

async function main() {
  const communitySlugs = argv['community-slugs']
  const yes = argv['yes']

  const memberships = await getMembershipsBySlugs(communitySlugs)
  console.log(
    `Running tenant creation process for ${
      memberships.length > 0 ? memberships.map((m) => m.slug) : 'all communities'
    }\n`,
  )

  if (!yes) {
    const shouldProceed = await confirmScriptExecution()

    if (!shouldProceed) return
  }

  addKnockTenants(memberships)
}

main()
