const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const searchString = 'picking your niche'
const databaseName = 'memberup-dev'

async function searchInDatabase() {
  try {
    const tables = await prisma.$queryRaw`SHOW TABLES`

    for (const table of tables) {
      const tableName = table[`Tables_in_${databaseName}`]
      const fields = await prisma.$queryRawUnsafe(`DESCRIBE ${tableName}`)

      for (const field of fields) {
        if (field && field.f0) {
          const fieldName = field.f0
          // Constructing the query string in JavaScript
          const query = `SELECT * FROM \`${tableName}\` WHERE LOWER(\`${fieldName}\`) LIKE '%${searchString.toLowerCase()}%'`

          const results = await prisma.$queryRawUnsafe(query)

          if (results.length > 0) {
            console.log(`Found in ${tableName}.${fieldName}:`)
            results.forEach((result) => {
              console.log(result)
            })
          }
        } else {
          console.log(`No valid field found for table ${tableName}`)
        }
      }
    }
  } catch (err) {
    console.error('Error:', err)
  } finally {
    await prisma.$disconnect()
  }
}

searchInDatabase()

/* 

doppler run -- node apps/memberup/scripts/find-data-by-string-value.js

*/
