const { PrismaClient } = require('@prisma/client')
const axios = require('axios')
const fs = require('fs').promises
const path = require('path')
const prisma = new PrismaClient()

const REWARDFUL_API_KEY = process.env.REWARDFUL_API_KEY
const REWARDFUL_ENDPOINT = 'https://api.getrewardful.com/v1/affiliates'
const USERS_FILE_PATH = path.join(__dirname, 'rewardfulUsersWithNames.json')

const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

const fetchRewardfulUsers = async () => {
  let page = 1
  let users = []
  while (true) {
    console.log('Fetching page from Rewardful API:', page)
    const response = await axios.get(`${REWARDFUL_ENDPOINT}?page=${page}&limit=100`, {
      headers: { Authorization: `Bearer ${REWARDFUL_API_KEY}` },
    })
    const rewardfulUsers = response.data.data
    rewardfulUsers.forEach((user) => {
      users.push({
        stripe_customer_id: user.stripe_customer_id,
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
      }) // Use Rewardful user ID
    })
    if (rewardfulUsers.length === 0) break
    page++
    await delay(1000)
  }
  await fs.writeFile(USERS_FILE_PATH, JSON.stringify(users, null, 2))
  return users
}

const updateAffiliateName = async (affiliateId, firstName, lastName) => {
  const data = {
    first_name: firstName,
    last_name: lastName,
  }
  console.log('updating', lastName, 'to', data.first_name, data.last_name)
  const response = await axios.put(`https://api.getrewardful.com/v1/affiliates/${affiliateId}`, data, {
    auth: {
      username: REWARDFUL_API_KEY,
      password: '',
    },
  })
  return response.data
}

const processUsers = async () => {
  let users
  try {
    await fs.access(USERS_FILE_PATH)
    console.log('Users file found. Reading users from file...')
    users = JSON.parse(await fs.readFile(USERS_FILE_PATH, 'utf8'))
  } catch (error) {
    console.log('Users file not found. Fetching users from Rewardful API...')
    users = await fetchRewardfulUsers()
  }

  let processedCount = 0

  for (const user of users) {
    if (!user.stripe_customer_id) {
      console.log(`Skipping user ${user.id} due to null stripe_customer_id.`)
      continue
    }

    if (user.first_name === '_' && user.last_name === '_') {
      // Fetch all memberships where the user is an owner
      const userEmail = user.email // Assuming 'user.email' contains the email of the user you're interested in
      const membershipsQuery = `
            SELECT m.*, u.id AS user_id, u.first_name, u.last_name
            FROM memberships m
            JOIN users u ON m.id = u.membership_id AND u.email = ? AND u.role = 'owner'
            `

      const memberships = await prisma.$queryRawUnsafe(membershipsQuery, userEmail)
      const communityWithUserSet = memberships.find((membership) => membership.first_name || membership.last_name)

      if (!communityWithUserSet?.first_name && !communityWithUserSet?.last_name) {
        console.log(
          `Name on the database is empty for ${user.email}, skipping updating affiliate name on rewardful db.`,
        )
        continue
      }

      /* updateAffiliateName  */
      console.log(
        `Updating affiliate name for user with Rewardful ID ${user.id}... with ${communityWithUserSet.first_name} ${communityWithUserSet.last_name}`,
      )
      try {
        const updatedAffiliateData = await updateAffiliateName(
          user.id,
          communityWithUserSet.first_name,
          communityWithUserSet.last_name,
        )
        console.log(`Updated affiliate name for user with Rewardful ID ${user.id} ${user.email}`)
        /* update all memberships with this affiliate data */
        for (const membership of memberships) {
          if (updatedAffiliateData) {
            console.log(`Updating affiliate name for membership with ID ${membership.id}...`)
            await prisma.membershipSetting.update({
              where: { membership_id: membership.id },
              data: { affiliate: updatedAffiliateData },
            })
          }
        }
        ++processedCount
      } catch (error) {
        console.log('Failed to update affiliate name for', error)
      }
    } else {
      console.log('name is already set, skipping')
      continue
    }
  }
  console.log(`Processing completed. Total emails marked as deprecated: ${processedCount}`)
}

processUsers().catch(console.error)
/*
REWARDFUL_API_KEY=your_production_api_key doppler run -- node apps/memberup/scripts/rewardly-set-names-to-emails.js
*/
