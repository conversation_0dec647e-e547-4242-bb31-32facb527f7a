require('dotenv').config()

const { PrismaClient } = require('@prisma/client')
const { sparkCategories, university } = require('./data.js')
const prisma = new PrismaClient()

const load = async () => {
  try {
    // await prisma.sparkResponse.deleteMany()
    // console.log('Deleted records in Spark Response')

    // await prisma.sparkMembershipQuestion.deleteMany()
    // console.log('Deleted records in Spark Membership Question')

    // await prisma.sparkMembershipCompletedQuestion.deleteMany()
    // console.log('Deleted records in Spark Membership Completed Question')

    // await prisma.sparkQuestion.deleteMany()
    // console.log('Deleted records in Spark Question')

    // await prisma.sparkMembershipCategory.deleteMany()
    // console.log('Deleted records in Spark Membership Category')

    // await prisma.sparkCategory.deleteMany()
    // console.log('Deleted records in Spark Category')

    // await prisma.membershipSetting.updateMany({
    //   data: {
    //     active_spark_category_id: null,
    //   },
    // })
    // console.log('Removed active_spark_category_id from MembershipSetting')

    // // await prisma.$queryRaw`ALTER TABLE Product AUTO_INCREMENT = 1`
    // // console.log('reset product auto increment to 1')

    // for (const c of sparkCategories) {
    //   await prisma.sparkCategory.create({
    //     data: {
    //       name: c.name,
    //       slug: c.slug,
    //       icon: c.icon,
    //       description: c.description,
    //       spark_questions: {
    //         createMany: {
    //           data: c.spark_questions,
    //         },
    //       },
    //       seed_id: c.seed_id,
    //       membership_id: null,
    //     },
    //   })
    // }

    /* create an array of action objects, it just has likes for now with a 1 point value */
    const actions = [
      {
        action_name: 'LIKE',
        points: 1,
      },
      {
        action_name: 'USER_ACTIVE',
        points: 1,
      },
      {
        action_name: 'POST_CREATED',
        points: 1,
      },
      {
        action_name: 'COMMENT_CREATED',
        points: 1,
      },
      {
        action_name: 'LESSON_COMPLETED',
        points: 1,
      },
    ]

    for (const action of actions) {
      const existingAction = await prisma.action.findFirst({
        where: {
          action_name: action.action_name,
        },
      })

      if (!existingAction) {
        await prisma.action.create({
          data: {
            action_name: action.action_name,
            points: action.points,
          },
        })
      }
    }

    const existingSCs = await prisma.sparkCategory.findMany()
    const existingSQs = await prisma.sparkQuestion.findMany()

    for (const c of sparkCategories) {
      const existingSC = existingSCs.find((item) => item.seed_id === c.seed_id)
      if (existingSC?.id) {
        await prisma.sparkCategory.update({
          data: {
            name: c.name,
            slug: c.slug,
            icon: c.icon,
            description: c.description,
          },
          where: {
            id: existingSC.id,
          },
        })

        for (const q of c.spark_questions) {
          const existingSQ = existingSQs.find((item) => item.seed_id === q.seed_id)
          if (existingSQ?.id) {
            await prisma.sparkQuestion.update({
              data: {
                content: q.content,
                membership_id: q.membership_id,
                sequence: q.sequence,
              },
              where: {
                id: existingSQ.id,
              },
            })
          } else {
            await prisma.sparkQuestion.create({
              data: {
                content: q.content,
                membership_id: q.membership_id,
                sequence: q.sequence,
                seed_id: q.seed_id,
                category: {
                  connect: {
                    id: existingSC.id,
                  },
                },
              },
            })
          }
        }
      } else {
        await prisma.sparkCategory.create({
          data: {
            name: c.name,
            slug: c.slug,
            icon: c.icon,
            description: c.description,
            spark_questions: {
              createMany: {
                data: c.spark_questions,
              },
            },
            seed_id: c.seed_id,
            membership_id: null,
          },
        })
      }

      if (c?.name) {
        console.log(`Added category: ${c.name} with questions`)
      }
    }
  } catch (e) {
    console.error(e)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

load()
