const { StreamChat } = require('stream-chat')
const { askQuestion, getStreamApps } = require('./common')
const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET
const DATABASE_URL = process.env.DATABASE_URL
const client = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
  timeout: 30000,
})

const main = async () => {
  console.log(`Using Database URL ${DATABASE_URL}`)
  console.log(`Using GetStream App: key=${GET_STREAM_APP_KEY}, name=${getStreamApps[GET_STREAM_APP_KEY].name}`)

  const answer = await askQuestion('Do you want to proceed? (y/n): ')
  if (answer.toLowerCase() !== 'y') {
    console.log('Good bye.')
    return
  }

  const feeds = await prisma.feed.findMany({
    where: {
      feed_status: 'active',
      feed_type: 'default',
      //id: 'b5b7fdda-01aa-4157-a60b-f2b8db93cfcb',
    },
  })

  let i = 0
  for (const post of feeds) {
    i++
    try {
      console.log(`Processing ${i} of ${feeds.length}`)
      const response = await client.getMessage(post.id)
      const message = response.message

      let last_activity_at
      if (message.latest_comment_timestamp) {
        last_activity_at = new Date(message.latest_comment_timestamp * 1000).toISOString()
      } else {
        last_activity_at = message.created_at
      }
      await client.partialUpdateMessage(
        message.id,
        {
          set: { last_activity_at: last_activity_at },
          unset: [],
        },
        post.user_id,
      )
      await prisma.feed.update({
        where: {
          id: post.id,
        },
        data: {
          last_activity_at: last_activity_at,
        },
      })
    } catch (e) {
      console.log(e.message)
    }
  }
}

main()
