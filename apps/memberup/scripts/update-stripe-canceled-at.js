require('dotenv').config()

const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const updateStripeCanceledAt = async () => {
  try {
    const data = await prisma.membershipSetting.findMany({
      where: {
        stripe_subscription_canceled_at1: { not: null },
      },
    })

    for (const item of data) {
      const temp = await prisma.membershipSetting.update({
        where: {
          id: item.id,
        },
        data: {
          stripe_subscription_canceled_at: Math.floor(new Date(item.stripe_subscription_canceled_at1).getTime() / 1000),
        },
      })
      console.log('temp =====', temp.stripe_subscription_canceled_at)
    }
  } catch (e) {
    console.error(e)
    process.exit(1)
  } finally {
  }
}

updateStripeCanceledAt()
