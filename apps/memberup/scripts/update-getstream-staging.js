require('dotenv').config()
const { StreamChat } = require('stream-chat')
const { PrismaClient } = require('@prisma/client')
const fs = require('fs').promises
const path = require('path')
const yargs = require('yargs/yargs')
const { hideBin } = require('yargs/helpers')

const prisma = new PrismaClient()

console.log('Production: ', process.env.PRODUCTION_GETSTREAM_APP_KEY, process.env.PRODUCTION_GETSTREAM_APP_SECRET)
console.log('Staging: ', process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY, process.env.GET_STREAM_APP_SECRET)

const prodClient = new StreamChat(process.env.PRODUCTION_GETSTREAM_APP_KEY, process.env.PRODUCTION_GETSTREAM_APP_SECRET)
const stagingClient = new StreamChat(process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY, process.env.GET_STREAM_APP_SECRET)

const exportFolderPath = 'exports'

const argv = yargs(hideBin(process.argv))
  .option('community-slugs', {
    type: 'string',
    description: 'Comma-separated list of community slugs',
    coerce: (arg) => (arg ? arg.split(',') : []),
  })
  .option('days-before', {
    type: 'number',
    description: 'Number of days before the current date to set as the cutoff date',
  }).argv

const communitySlugs = argv['community-slugs']
const daysBefore = argv['days-before']

const cutoffDate = daysBefore ? new Date(Date.now() - daysBefore * 24 * 60 * 60 * 1000) : null

function delay(ms) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

const saveToFile = async (filename, data) => {
  console.log(`Saving ${filename}...`)
  await fs.writeFile(path.join(exportFolderPath, filename), JSON.stringify(data, null, 2), 'utf-8')
  console.log(`${filename} saved successfully.`)
}

const fetchUserIdsFromDB = async (communityIds) => {
  console.log('Fetching user IDs from database...', communityIds)
  const users = await prisma.user.findMany({
    where: {
      membership_id: {
        in: communityIds,
      },
    },
    select: {
      id: true,
    },
  })
  console.log(`Fetched ${users.length} user IDs.`)
  return users.map((user) => user.id.toString())
}

const fetchAndSaveUsers = async (userIds) => {
  console.log('Fetching and saving users...')
  let users = []
  let offset = 0
  let limit = 100
  let total = 0
  do {
    try {
      const response = await prodClient.queryUsers({ id: { $in: userIds } }, {}, { limit, offset })
      users = users.concat(
        response.users.map((user) => {
          delete user['created_at']
          delete user['updated_at']
          delete user['deleted_at']
          delete user['last_active']
          delete user['online']
          return user
        }),
      )
      total = response.users.length
      console.log(`Fetched ${total} users at offset ${offset}.`)
      offset += limit
    } catch (error) {
      console.error('Error fetching users:', error)
      process.exit(1)
      break
    }
  } while (total === limit)

  await saveToFile('users.json', users)
}

const getMembershipIdsBySlugs = async (communitySlugs) => {
  console.log('Fetching membership from database...', communitySlugs)
  const communityIds = await prisma.membership.findMany({
    where: {
      slug: {
        in: communitySlugs?.length ? communitySlugs : undefined,
      },
    },
    select: {
      id: true,
    },
  })
  const communityIdsList = communityIds.map((community) => community.id)
  console.log('Fetched membership IDs:', communityIdsList)
  return communityIdsList
}

const fetchAndSaveChannels = async (communityIds) => {
  console.log('Fetching and saving channels...')
  let channels = []
  let offset = 0
  let limit = 100
  let total = 0
  do {
    try {
      const response = await prodClient.queryChannels(
        { team: { $in: communityIds } },
        { last_message_at: -1 },
        {
          limit,
          offset,
          state: true,
        },
      )
      channels = channels.concat(response)
      total = response.length
      console.log(`Fetched ${total} channels at offset ${offset}.`)
      offset += limit
    } catch (error) {
      console.error('Error fetching channels:', error)
      process.exit(1)
      break
    }
  } while (total === limit)

  for (const channel of channels) {
    await saveToFile(`channel_${channel.id}.json`, channel.data)
    await saveToFile(
      `messages_${channel.id}.json`,
      channel.state.messages.filter((message) => !cutoffDate || new Date(message.created_at) >= cutoffDate),
    )
  }
}

const importUsers = async () => {
  console.log('Importing users...')
  const usersData = JSON.parse(await fs.readFile(path.join(exportFolderPath, 'users.json'), 'utf-8'))
  const chunks = []
  while (usersData.length) {
    chunks.push(usersData.splice(0, 100))
  }

  for (const chunk of chunks) {
    try {
      await stagingClient.upsertUsers(chunk)
      console.log(`Imported ${chunk.length} users.`)
    } catch (error) {
      console.error('Error importing users:', error)
      process.exit(1)
    }
  }
}

const importChannelsAndMessages = async () => {
  console.log('Importing channels and messages...')
  const files = await fs.readdir(exportFolderPath)
  const channelFiles = files.filter((file) => file.startsWith('channel_') && file.endsWith('.json'))
  for (const file of channelFiles) {
    let channelData = JSON.parse(await fs.readFile(path.join(exportFolderPath, file), 'utf-8'))
    const channelType = channelData['type']
    const channelId = channelData['id']
    if (channelId.includes('!')) continue
    delete channelData.config
    delete channelData['id']
    delete channelData['type']
    delete channelData['created_at']
    delete channelData['updated_at']
    delete channelData['cid']
    delete channelData['member_count']
    delete channelData['last_message_at']
    delete channelData.created_by.created_at
    delete channelData.created_by.last_active
    delete channelData.created_by.updated_at
    const messagesData = JSON.parse(
      await fs.readFile(path.join(exportFolderPath, `messages_${channelId}.json`), 'utf-8'),
    )
    let channel = null
    try {
      channel = stagingClient.channel(channelType, channelId, channelData)
      await channel.create()
    } catch (error) {
      /*  console.error(`Error creating channel ${channelId}:`, error); */
      continue
    }
    console.log(`Channel ${channelId} created.`, messagesData)

    for (const message of messagesData) {
      try {
        if (message.id.includes('!')) continue

        delete message.created_at
        delete message.latest_reactions
        delete message.own_reactions
        delete message.reaction_counts
        delete message.reply_count
        delete message.updated_at

        await channel.sendMessage(message)
        console.log(`Message ${message.id} sent in channel ${channelId}.`)
      } catch (error) {
        /* console.error(`Error sending message ${message.id} in channel ${channelId}:`, error); */
      }
    }
    console.log(`Imported all messages for channel ${channelId}.`)
  }
  console.log('All channels and messages imported successfully.')
}

const main = async () => {
  console.log('Starting data cleanup...')
  /* delete exports directory */
  console.log('deleting previous export files')
  try {
    await fs.rmdir(exportFolderPath, { recursive: true })
  } catch (e) {}

  const communityIds = await getMembershipIdsBySlugs(communitySlugs)

  const userIds = await fetchUserIdsFromDB(communityIds)

  console.log('Starting data migration process...')
  try {
    await fs.mkdir(exportFolderPath, { recursive: true })
    await fetchAndSaveUsers(userIds)
    await fetchAndSaveChannels(communityIds)
    await importUsers()
    await importChannelsAndMessages()
    console.log('Data migration completed successfully.')
  } catch (error) {
    console.error('Error during data migration:', error)
    process.exit(1)
  }
}

main()

/* 

doppler run -- node  apps/memberup/scripts/update-getstream-staging.js --communitySlugs="slug1,slug2" --daysBefore=30   if no communitySlugs are provided, all communities will be considered. If no daysBefore is provided, all messages will be considered.

*/
