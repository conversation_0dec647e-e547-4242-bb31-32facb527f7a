const { PrismaClient, Prisma } = require('@prisma/client')
const axios = require('axios')
const prisma = new PrismaClient()

const REWARDFUL_API_SECRET = process.env.REWARDFUL_API_SECRET
const UPDATE_JUST_ONE_AFFILIATE = process.env.UPDATE_JUST_ONE_AFFILIATE === 'true' // Check if test mode is enabled

const createRewardfulAffiliate = async (api_secret, data) => {
  try {
    const result = await axios.post(`https://api.getrewardful.com/v1/affiliates`, data, {
      auth: {
        username: api_secret,
        password: '',
      },
    })
    return result.data
  } catch (err) {
    console.log(err?.response?.data)
    throw err
  }
}

const processMemberships = async () => {
  const rawQuery = `
    SELECT m.id AS membership_id, u.email, u.first_name, u.last_name, ms.stripe_customer_id
    FROM memberships m
    INNER JOIN membership_settings ms ON ms.membership_id = m.id
    INNER JOIN users u ON u.membership_id = m.id
    WHERE m.active = 1 AND ms.affiliate IS NULL AND u.role = 'owner'
  `
  console.log('Fetching memberships...')
  const memberships = await prisma.$queryRaw(Prisma.raw(rawQuery))
  console.log(`Total memberships to process: ${memberships.length}`)

  let processedCount = 0

  for (const { membership_id, email, first_name, last_name, stripe_customer_id } of memberships) {
    const affiliateData = {
      first_name: first_name || ' ',
      last_name: last_name || '_',
      email,
      stripe_customer_id,
    }

    try {
      console.log(`Processing (${++processedCount}/${memberships.length}): ${email}`)
      const newAffiliate = await createRewardfulAffiliate(REWARDFUL_API_SECRET, affiliateData)
      await prisma.membershipSetting.update({
        where: { membership_id },
        data: { affiliate: newAffiliate },
      })
      console.log(`New affiliate created for ${email}`)
    } catch (error) {
      console.log(`Failed to create new affiliate for ${email}:`, error.message)
    }
    if (UPDATE_JUST_ONE_AFFILIATE) {
      console.log('UPDATE_JUST_ONE_AFFILIATE is enabled, stopping after processing one membership.')
      break
    }
  }
  console.log(`Processing completed. Total processed memberships: ${processedCount}`)
}

processMemberships()

/* 

doppler run -- node  apps/memberup/scripts/rewardly-affiliate-multiple-communities-check.js
UPDATE_JUST_ONE_AFFILIATE=true doppler run -- node apps/memberup/scripts/generate-affiliate-links.js if you want to execute just one
*/
