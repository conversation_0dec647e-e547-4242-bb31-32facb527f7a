const fs = require('fs')
const path = require('path')

const mappingFilePath = path.join(__dirname, 'github_to_slack_mapping.json')

function getSlackMemberId(githubUsername) {
  try {
    const mapping = JSON.parse(fs.readFileSync(mappingFilePath, 'utf8'))
    return mapping[githubUsername] || ''
  } catch (error) {
    console.error('Error reading or parsing the mapping file:', error)
    return ''
  }
}

const githubUsernames = process.argv[2]
if (!githubUsernames) {
  const mapping = JSON.parse(fs.readFileSync(mappingFilePath, 'utf8'))
  console.log(Object.values(mapping).join(','))
} else {
  const slackMemberIds = githubUsernames.split(',').map((githubUsername) => getSlackMemberId(githubUsername))
  if (slackMemberIds.length > 0) {
    console.log(slackMemberIds.join(','))
  } else {
    console.error(`No Slack Member ID found for GitHub username: ${githubUsernames}`)
  }
}
