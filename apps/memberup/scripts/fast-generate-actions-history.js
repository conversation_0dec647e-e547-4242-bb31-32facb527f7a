// Ensure Doppler is set up to run on the staging environment
// doppler run -- node  apps/memberup/scripts/generate-actions-history.js

require('dotenv').config()
const { PrismaClient, Prisma } = require('@prisma/client')
const { askQuestion, getStreamApps } = require('./common')

const prisma = new PrismaClient()

const DATABASE_URL = process.env.DATABASE_URL

const generateActionsHistory = async () => {
  console.log(`Using Database URL ${DATABASE_URL}`)
  const answer = await askQuestion('Do you want to proceed? (y/n): ')
  if (answer.toLowerCase() !== 'y') {
    console.log('Good bye.')
    return
  }

  const likeAction = await prisma.action.findUnique({
    where: {
      action_name: 'LIKE',
    },
  })
  const postCreatedAction = await prisma.action.findUnique({
    where: {
      action_name: 'POST_CREATED',
    },
  })
  const commentCreatedAction = await prisma.action.findUnique({
    where: {
      action_name: 'COMMENT_CREATED',
    },
  })

  // const users = await prisma.user.findMany({
  //     where: {
  //         membership_id: 'a928fd24-3d7e-4ae7-aa27-3764377341f2'
  //     },
  //     orderBy: {
  //         id: 'desc'
  //     },
  //     select: {
  //         id: true
  //     }
  // })

  const users = await prisma.$queryRaw(
    Prisma.raw(`SELECT DISTINCT user_id as id FROM feeds UNION SELECT DISTINCT user_id as id FROM reactions;`),
  )
  let count = 0

  // Create Post Actions
  const insertCreatePostsAction = `INSERT INTO action_history (id, user_id, action_id, resource_id, createdAt)
        SELECT UUID(), user_id, '${postCreatedAction.id}', id as resource_id, createdAt
        FROM feeds WHERE feed_type = 'default';`
  await prisma.$queryRaw(Prisma.raw(insertCreatePostsAction))

  // Create Comment Actions
  const insertCreateCommentAction = `INSERT INTO action_history (id, user_id, action_id, resource_id, createdAt)
        SELECT UUID(), user_id, '${commentCreatedAction.id}', id as resource_id, createdAt
        FROM feeds
        WHERE feed_type = 'comment';`
  await prisma.$queryRaw(Prisma.raw(insertCreateCommentAction))

  // Like Actions
  const insertLikeAction = `INSERT INTO action_history (id, user_id, action_id, resource_id, createdAt)
        select UUID(), user_id, '${likeAction.id}' as action_id, message_id as resource_id, max(createdAt) as createdAt from reactions where type = 'like' group by user_id, message_id`
  await prisma.$queryRaw(Prisma.raw(insertLikeAction))

  // Generate actions per day from action history
  const insertActionsPerDay = `INSERT INTO actions_per_day (id, user_id, day, count)
        SELECT UUID(), user_id, DATE(createdAt) as day, COUNT(*) as count
        FROM action_history
        GROUP BY user_id, DATE(createdAt);`
  await prisma.$queryRaw(Prisma.raw(insertActionsPerDay))
}

generateActionsHistory()
