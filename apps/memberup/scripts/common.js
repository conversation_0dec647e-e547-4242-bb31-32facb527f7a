import readline from 'readline'

export const confirmScriptExecution = async () => {
  const answer = await askQuestion(
    'You are currently on Doppler config ' + process.env.DOPPLER_CONFIG + '.\n\nDo you want to proceed? (y/N): ',
  )

  return answer.toLowerCase() === 'y'
}

export const askQuestion = (query) => {
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
  })

  return new Promise((resolve) => {
    rl.question(query, (answer) => {
      resolve(answer)
      rl.close()
    })
  })
}

// TODO: this could be improved
export const getStreamApps = {
  qahekpw8bad3: {
    name: 'dev-migration',
  },
  ng3wxmuq5v96: {
    name: 'dev',
  },
  f3mbfbvvwabc: {
    name: 'production',
  },
}

// TODO: this could be improved
export const getKnockApps = {
  sk_test_BlhIQLXt2heT1cuPg87WRaR25454SScNlFyYWyEYPcA: {
    name: 'dev',
  },
  sk_eo7FMHAY87NZl6GlTzlFQbTpJDExMDyzjfGWvXNgV3Y: {
    name: 'staging',
  },
  'sk_LhjSO-wg8bwZjhzsoLRZzHAuO_QLYP7tl6Ca0LbIjoU': {
    name: 'production',
  },
}
