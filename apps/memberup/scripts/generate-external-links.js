require('dotenv').config()
const { askQuestion, getStreamApps } = require('./common')
const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const DATABASE_URL = process.env.DATABASE_URL
async function main() {
  console.log(`Using Database URL ${DATABASE_URL}`)
  const answer = await askQuestion('Do you want to proceed? (y/n): ')
  if (answer.toLowerCase() !== 'y') {
    console.log('Good bye.')
    return
  }

  const membershipSettings = await prisma.membershipSetting.findMany({
    where: {
      affiliate: { not: null },
      //external_links: { equals: null}
    },
    select: {
      id: true,
      affiliate: true,
    },
    orderBy: [{ id: 'desc' }],
  })

  let i = 0
  for (let ms of membershipSettings) {
    i++
    console.log(`Processing ${i} of ${membershipSettings.length}`)
    await prisma.membershipSetting.update({
      where: {
        id: ms.id,
      },
      data: {
        external_links: ms.affiliate?.links?.[0]?.url
          ? [{ url: ms.affiliate?.links?.[0]?.url, label: 'Create Your Own Community' }]
          : undefined,
      },
    })
  }
}

main()
