const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const getFileType = (fileType) => {
  if (!fileType) return 'other'
  if (fileType.includes('video')) return 'video'
  if (fileType.includes('image')) return 'image'
  if (fileType.includes('audio')) return 'audio'
  if (fileType.includes('pdf')) return 'pdf'
  if (fileType.includes('doc')) return 'doc'
  if (fileType.includes('ppt')) return 'ppt'
  if (fileType.includes('epub')) return 'epub'
  return 'other'
}

async function updateResourceFiles() {
  try {
    const lessons = await prisma.contentLibraryCourseLesson.findMany()
    const lessonsToUpdate = lessons.filter((lesson) => lesson.resource_files !== null)
    const totalLessons = lessonsToUpdate.length
    let processedLessons = 0

    for (const lesson of lessonsToUpdate) {
      const updatedResourceFiles = lesson.resource_files.map((file) => {
        if (!file.filename || !file.mimetype || !file.size_in_bytes) {
          return {
            ...file,
            filename: file.filename || file.name,
            mimetype: file.mimetype || getFileType(file.type),
            size_in_bytes: file.size_in_bytes || file.size,
          }
        }
        return file
      })

      processedLessons++
      console.log(`Processing lesson ${processedLessons} of ${totalLessons}`)

      if (updatedResourceFiles.length > 0) {
        await prisma.contentLibraryCourseLesson.update({
          where: { id: lesson.id },
          data: { resource_files: updatedResourceFiles },
        })
      }
    }
  } catch (error) {
    console.error('Error updating resource files:', error)
  } finally {
    await prisma.$disconnect()
    console.log('Disconnected from Prisma.')
  }
}

updateResourceFiles()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
/* 

doppler run -- node apps/memberup/scripts/update-resource-files-migrations.js

*/
