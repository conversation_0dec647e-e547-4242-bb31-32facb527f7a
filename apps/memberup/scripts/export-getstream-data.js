//import { fetch } from 'node-fetch'

//require('dotenv').config()

const fetch = require('node-fetch')
const fs = require('fs').promises
const { StreamChat } = require('stream-chat')

const { PrismaClient } = require('@prisma/client')

const { askQuestion, getStreamApps } = require('./common')

const prisma = new PrismaClient({
  log: [
    {
      emit: 'event',
      level: 'query',
    },
  ],
})

prisma.$on('query', (e) => {
  console.log(e.query, e.params)
})

const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET

const fetchDataAndSaveToFile = async (outputFilename, url) => {
  const response = await fetch(url)
  //const data = await response.json()
  const data = await response.text()
  //const jsonString = JSON.stringify(data, null, 2)
  //await fs.writeFile(outputFilename, jsonString, 'utf-8')
  await fs.writeFile(outputFilename, data, 'utf-8')
}

const exportChannels = async (serverClient, exportId, channels, chunkId) => {
  const outputFilename = `${exportId}/channel-export-${chunkId}.json`

  const response = await serverClient.exportChannels(channels, {
    version: 'v2',
  })
  const taskId = response.task_id

  const checkCondition = async () => {
    const exportStatus = await serverClient.getExportChannelStatus(taskId)
    // TODO: When failing we want to identify the taskId and the associated channels.
    if (exportStatus.error) {
      throw new Error(exportStatus.error)
    }
    if (exportStatus.status !== 'completed') {
      console.log(exportStatus.status)
      return false
    }
    return exportStatus.result.url
  }

  const checkConditionAsPromise = () => {
    return new Promise((resolve, reject) => {
      let intervalId
      const checkAndClearInterval = async () => {
        try {
          const exportUrl = await checkCondition()
          if (exportUrl) {
            clearInterval(intervalId)
            resolve(exportUrl)
          } else {
            console.log('.')
          }
        } catch (error) {
          clearInterval(intervalId)
          reject(error)
        }
      }
      intervalId = setInterval(checkAndClearInterval, 5000) // Check every 5 seconds
    })
  }

  // Run the check immediately, then continue at intervals
  const exportUrl = await checkConditionAsPromise()

  await fetchDataAndSaveToFile(outputFilename, exportUrl)
  return outputFilename
}

const generateJSONL = async (users, filename) => {
  // Open a file handle for writing
  const filehandle = await fs.open(filename, 'w')

  try {
    for (const user of users) {
      // Convert each user object to a JSON string
      const jsonString = JSON.stringify(user)
      // Write the stringified object to the file followed by a newline
      await filehandle.write(jsonString + '\n')
    }
  } finally {
    // Close the file handle when done
    await filehandle.close()
  }

  console.log(`Generated JSONL file ${filename}`)
}

const generateExportId = () => {
  const currentDateTime = new Date().toISOString()
  const sanitizedDateTime = currentDateTime.replace(/:/g, '-').replace(/\.\d+Z$/, '')
  const exportId = `getstream-export-${sanitizedDateTime}`
  return exportId
}

async function main() {
  try {
    const membershipId = process.argv[2] // Accept file path as a command-line argument
    console.log(`Running export process for ${membershipId ? 'membership_id:' + membershipId : 'all communities'}`)
    console.log(`Using GetStream App: key=${GET_STREAM_APP_KEY}, name=${getStreamApps[GET_STREAM_APP_KEY].name}`)

    const answer = await askQuestion('Do you want to proceed? (y/n): ')
    if (answer.toLowerCase() !== 'y') {
      console.log('Good bye.')
      return
    }

    const where = membershipId ? { where: { membership_id: membershipId } } : {}
    const dbUsers = await prisma.user.findMany({
      where: where,
      select: {
        id: true,
      },
    })

    if (dbUsers.length === 0) {
      console.error(`Error: Cannot find users`)
      process.exit(1)
    }

    const dbUserIds = dbUsers.map((u) => u.id)

    const serverClient = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
      timeout: 30000,
    })

    console.log(`Exporting ${dbUserIds.length} users from Getstream`)

    let allUsers = []
    let chunkOffset = 0
    let chunkSize = 100
    while (true) {
      const selectedDbUserIds = dbUserIds.slice(chunkOffset, chunkOffset + chunkSize)
      console.log(selectedDbUserIds.length, chunkOffset, chunkSize)
      if (selectedDbUserIds.length === 0) {
        break
      }
      const usersResponse = await serverClient.queryUsers(
        { id: { $in: selectedDbUserIds } },
        { id: -1 }, // sort
        { limit: 100 }, // options
      )
      allUsers = allUsers.concat(usersResponse.users)
      chunkOffset += chunkSize
    }

    const exportId = generateExportId()
    console.log(`Export session ID: ${exportId}`)
    await fs.mkdir(exportId, { recursive: true })

    const usersFilename = `${exportId}/users.json`

    await generateJSONL(
      allUsers.map((u) => ({ type: 'user', item: u })),
      usersFilename,
    )

    const dbChannels = await prisma.channel.findMany({
      where: where,
      select: {
        id: true,
      },
      orderBy: {
        id: 'asc',
      },
    })

    const dbChannelIds = dbChannels.map((c) => c.id)
    if (dbChannelIds.length === 0) {
      console.log('No channels found. Quitting.')
      process.exit(0)
    }

    let allChannels = []
    chunkSize = 300
    chunkOffset = 0
    while (true) {
      const selectedChannelsIds = dbChannelIds.slice(chunkOffset, chunkOffset + chunkSize)
      if (selectedChannelsIds.length === 0) {
        break
      }
      const channelsResponse = await serverClient.queryChannels(
        { id: { $in: selectedChannelsIds } },
        { id: -1 },
        { limit: 300 },
      )
      const channels = channelsResponse.map((c) => ({ type: c.type, id: c.id }))
      allChannels = allChannels.concat(channels)
      chunkOffset += chunkSize
    }

    const channelsInChunks = []
    chunkSize = 25 // Define the size of each chunk
    for (let i = 0; i < allChannels.length; i += chunkSize) {
      const chunk = allChannels.slice(i, i + chunkSize)
      channelsInChunks.push(chunk)
    }

    const result = await Promise.all(
      channelsInChunks.map((channels, chunkId) => exportChannels(serverClient, exportId, channels, chunkId)),
    )
    console.log(result)
    console.log('Export completed.')
  } catch (e) {
    console.log('\nExport failed!')
    console.error(e)
    console.error(`Error: ${e.message}`)
  }
}
main()
