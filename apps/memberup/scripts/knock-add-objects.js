// Creates objects in Knock for all communities or a specific community
// Add --yes parameter for unattended operation
//
// Ensure Doppler is set up to run on the staging environment
// doppler run -- env TS_NODE_PROJECT=packages/shared/tsconfig.json node apps/memberup/scripts/knock-add-objects.js [Membership ID]

import { confirmScriptExecution } from './common.js'

import 'dotenv/config'

import { Knock } from '@knocklabs/node'
import { PrismaClient } from '@prisma/client'
import { hideBin } from 'yargs/helpers'
import yargs from 'yargs/yargs'

const prisma = new PrismaClient()

const KNOCK_API_KEY = process.env.KNOCK_SECRET_API_KEY

const argv = yargs(hideBin(process.argv))
  .option('community-slugs', {
    type: 'string',
    description: 'Comma-separated list of community slugs',
    coerce: (arg) => (arg ? arg.split(',') : []),
  })
  .option('yes', {
    type: 'boolean',
    description: 'Unattended operation',
  }).argv

const delay = (ms) => new Promise((res) => setTimeout(res, ms))

const getMembershipsBySlugs = async (communitySlugs) => {
  console.log('Fetching membership from database...', communitySlugs)
  const memberships = await prisma.membership.findMany({
    where: {
      slug: {
        in: communitySlugs?.length ? communitySlugs : undefined,
      },
    },
    select: {
      id: true,
      slug: true,
      name: true,
    },
  })
  return memberships
}

const addKnockObjects = async (memberships) => {
  const knockClient = new Knock(KNOCK_API_KEY)
  let count = 0
  for (let membership of memberships) {
    count++
    try {
      console.log(`====== Add Objects to ${membership.slug} collection ${count} of ${memberships.length} =======`)
      await knockClient.objects.bulkSet(membership.slug, [
        // Max 1 request per second
        {
          id: 'new-content',
          name: 'New Content Notification',
        },
        {
          id: 'new-event',
          name: 'New Event Notification',
        },
        {
          id: 'new-spark',
          name: 'New Spark Notification',
        },
        {
          id: 'new-everyone-mention',
          name: 'New Everyone Mention Notification',
        },
      ])
      console.log('====== Done =======')
      await delay(1000) // wait for 1 second
    } catch (err) {
      console.error('err ====', err)
    }
  }
  console.log('====== All memberships are done =======')
}

async function main() {
  const communitySlugs = argv['community-slugs']
  const yes = argv['yes']

  const memberships = await getMembershipsBySlugs(communitySlugs)
  console.log(
    `Running object subscription process for ${
      memberships.length > 0 ? memberships.map((m) => m.slug) : 'all communities'
    }`,
  )

  if (!yes) {
    const shouldProceed = await confirmScriptExecution()

    if (!shouldProceed) return
  }

  addKnockObjects(memberships)
}

main()
