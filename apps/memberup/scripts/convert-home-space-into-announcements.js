const { StreamChat } = require('stream-chat')
const { askQuestion, getStreamApps } = require('./common')
const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET
const DATABASE_URL = process.env.DATABASE_URL
const client = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
  timeout: 30000,
})

const main = async () => {
  console.log(`Using Database URL ${DATABASE_URL}`)
  console.log(`Using GetStream App: key=${GET_STREAM_APP_KEY}, name=${getStreamApps[GET_STREAM_APP_KEY].name}`)

  const answer = await askQuestion('Do you want to proceed? (y/n): ')
  if (answer.toLowerCase() !== 'y') {
    console.log('Good bye.')
    return
  }

  // Get the Get

  const channels = await prisma.channel.findMany({
    where: {
      space_type: 'home',
    },
  })

  let i = 0
  for (const channel of channels) {
    i++
    try {
      console.log(`Processing ${i} of ${channels.length}`)

      await prisma.channel.update({
        where: {
          id: channel.id,
        },
        data: {
          name: 'Announcements',
          slug: 'announcements',
          is_private: true,
          space_type: 'space',
          description: 'Share announcements!',
        },
      })
    } catch (e) {
      if (e.code === 'P2002') {
        console.log('Already found an announcements channel, we will just convert home to a private space.')
        await prisma.channel.update({
          where: {
            id: channel.id,
          },
          data: {
            is_private: true,
            space_type: 'space',
          },
        })
      }
    }
  }
}

main()
