const axios = require('axios')
const yargs = require('yargs/yargs')
const { hideBin } = require('yargs/helpers')
const fs = require('fs')

const argv = yargs(hideBin(process.argv))
  .option('backupName', {
    type: 'string',
    description: 'The backup name to restore',
  })
  .option('branchName', {
    type: 'string',
    description: 'The branch name to create',
  })
  .option('outputBranchPrefix', {
    type: 'string',
    description: 'The prefix for the output branch',
  })
  .option('databaseName', {
    type: 'string',
    description: 'The name of the database to restore the backup',
  }).argv

const { PLANETSCALE_SERVICE_TOKEN_ID, PLANETSCALE_SERVICE_TOKEN, PLANETSCALE_ORGANIZATION_NAME } = process.env

const apiHeaders = {
  Authorization: `${PLANETSCALE_SERVICE_TOKEN_ID}:${PLANETSCALE_SERVICE_TOKEN}`,
  'Content-Type': 'application/json',
}

async function main() {
  try {
    const { backupName, branchName, outputBranchPrefix, databaseName } = argv
    let backupId = null

    if (!databaseName) throw new Error('Please provide a database name.')

    await deleteNightlyBranches(databaseName)

    if (backupName) {
      console.log('Creating branch from specific backup...')
      backupId = await getBackupIdFromName(backupName, databaseName)
    } else if (branchName) {
      console.log(`Creating branch from latest backup for branch: ${branchName}...`)
      backupId = await getLatestBackupId(branchName, databaseName)
    } else {
      throw new Error('Please provide either a backup name or a branch name.')
    }

    const outputBranchName = `${outputBranchPrefix}-${Date.now()}`
    console.log(`Restoring backup to branch: ${outputBranchName}`)
    await restoreBackup(backupId, outputBranchName, databaseName)
    await waitForBranchReady(databaseName, outputBranchName)
    const credentials = await createBranchCredentials(outputBranchName, databaseName)

    console.log(`Backup restored to branch: ${outputBranchName}`)
    console.log(`Access credentials:`, credentials)

    // Write the connection string to a temporary file
    writeConnectionStringToFile(credentials.connection_string)
  } catch (error) {
    console.error('Error:', error)
    process.exit(-1)
  }
}

async function listBranches(databaseName) {
  const response = await axios.get(
    `https://api.planetscale.com/v1/organizations/${PLANETSCALE_ORGANIZATION_NAME}/databases/${databaseName}/branches`,
    {
      headers: apiHeaders,
    },
  )
  return response.data.data // Returns an array of branch objects
}

async function deleteBranch(databaseName, branchName) {
  const url = `https://api.planetscale.com/v1/organizations/${PLANETSCALE_ORGANIZATION_NAME}/databases/${databaseName}/branches/${branchName}`
  await axios.delete(url, { headers: apiHeaders })
  console.log(`Deleted branch: ${branchName}`)
}

async function deleteNightlyBranches(databaseName) {
  try {
    const branches = await listBranches(databaseName)
    const nightlyBranches = branches.filter((branch) => branch.name.startsWith('nightly-'))
    for (const branch of nightlyBranches) {
      await deleteBranch(databaseName, branch.name)
    }
  } catch (error) {
    console.error('Failed to delete nightly branches:', error)
    process.exit(-1)
  }
}

async function getLatestBackupId(branch, databaseName) {
  console.log(
    'getting latest backup for organization:',
    PLANETSCALE_ORGANIZATION_NAME,
    'database:',
    databaseName,
    'branch:',
    branch,
  )
  const response = await axios.get(
    `https://api.planetscale.com/v1/organizations/${PLANETSCALE_ORGANIZATION_NAME}/databases/${databaseName}/branches/${branch}/backups?page=1&per_page=1`,
    {
      headers: apiHeaders,
    },
  )

  const backups = response.data.data
  if (backups.length === 0) {
    throw new Error('No backups found.')
  }
  return backups[0].id
}

async function getBackupIdFromName(backupName, databaseName) {
  const response = await axios.get(
    `https://api.planetscale.com/v1/organizations/${PLANETSCALE_ORGANIZATION_NAME}/databases/${databaseName}/backups?page=1&per_page=20`,
    {
      headers: apiHeaders,
    },
  )

  const backups = response.data.data
  const matchingBackup = backups.find((backup) => backup.name === backupName)
  if (!matchingBackup) {
    throw new Error(`Backup with name ${backupName} not found.`)
  }

  return matchingBackup.id
}

async function restoreBackup(backupId, branchName, databaseName) {
  await axios.post(
    `https://api.planetscale.com/v1/organizations/${PLANETSCALE_ORGANIZATION_NAME}/databases/${databaseName}/branches`,
    {
      name: branchName,
      backup_id: backupId,
    },
    { headers: apiHeaders },
  )
}

async function waitForBranchReady(databaseName, branchName, timeout = 600000, interval = 10000) {
  const startTime = Date.now()
  const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms))
  console.log('Waiting for branch to be ready...')
  while (true) {
    const isReady = await isBranchReady(databaseName, branchName)
    if (isReady) {
      console.log('Branch is ready.')
      break
    } else if (Date.now() - startTime > timeout) {
      console.error('Timeout waiting for branch to be ready.')
      process.exit(-1)
    } else {
      console.log('Waiting for branch to be ready...')
      await delay(interval)
    }
  }
}

async function isBranchReady(databaseName, branchName) {
  const response = await axios.get(
    `https://api.planetscale.com/v1/organizations/${PLANETSCALE_ORGANIZATION_NAME}/databases/${databaseName}/branches/${branchName}`,
    {
      headers: apiHeaders,
    },
  )
  return response.data && response.data.ready
}

async function createBranchCredentials(branchName, databaseName) {
  const response = await axios.post(
    `https://api.planetscale.com/v1/organizations/${PLANETSCALE_ORGANIZATION_NAME}/databases/${databaseName}/branches/${branchName}/passwords`,
    {},
    {
      headers: apiHeaders,
    },
  )

  if (response.status !== 201) {
    console.error('Failed to create branch credentials.')
    process.exit(-1)
  }

  const { username, plain_text: password, access_host_url: host } = response.data
  const connectionString = `mysql://${username}:${password}@${host}:3306/${databaseName}?sslaccept=strict&connect_timeout=30&pool_timeout=30`
  return { username, password, connection_string: connectionString }
}

function writeConnectionStringToFile(connectionString) {
  const filePath = './db_connection_string.txt'
  fs.writeFile(filePath, connectionString, (err) => {
    if (err) {
      console.error('Failed to write connection string to file:', err)
      process.exit(1)
    }
    console.log('Connection string successfully written to file.')
  })
}

main()

/* 


doppler run -- node apps/memberup/scripts/restore-planetscale-backup.js --outputBranchPrefix nightly  --branchName="main" --databaseName="memberup-staging" will restore the latest backup for the main branch 
doppler run -- node apps/memberup/scripts/restore-planetscale-backup.js --outputBranchPrefix nightly --backupName="2024.02.08 15:20:05" --databaseName="memberup-staging" will restore the specific backup
doppler run -- node apps/memberup/scripts/restore-planetscale-backup.js --outputBranchPrefix nightly --backupName="2024.02.08 15:20:05" --branchName="main"  --databaseName="memberup-staging" will prioritize the backupName

*/
