const { PrismaClient } = require('@prisma/client')
const axios = require('axios')
const fs = require('fs').promises
const path = require('path')
const prisma = new PrismaClient()

const REWARDFUL_API_KEY = process.env.REWARDFUL_API_KEY
const REWARDFUL_ENDPOINT = 'https://api.getrewardful.com/v1/affiliates'
const USERS_FILE_PATH = path.join(__dirname, 'rewardfulUsers.json')

const delay = (ms) => new Promise((resolve) => setTimeout(resolve, ms))

const fetchRewardfulUsers = async () => {
  let page = 1
  let users = []
  while (true) {
    console.log('Fetching page from Rewardful API:', page)
    const response = await axios.get(`${REWARDFUL_ENDPOINT}?page=${page}}&limit=100`, {
      headers: { Authorization: `Bearer ${REWARDFUL_API_KEY}` },
    })
    const rewardfulUsers = response.data.data
    rewardfulUsers.forEach((user) => {
      users.push({
        stripe_customer_id: user.stripe_customer_id,
        id: user.id,
        email: user.email,
        first_name: user.first_name,
        last_name: user.last_name,
      }) // Use Rewardful user ID
    })
    if (rewardfulUsers.length === 0) break
    page++
    await delay(1000)
  }
  await fs.writeFile(USERS_FILE_PATH, JSON.stringify(users, null, 2))
  return users
}

const updateEmailToDeprecated = async (affiliateId, affiliateEmail) => {
  const currentTimestamp = new Date().getTime().toString()

  const data = {
    email: `deprecated_${currentTimestamp}_${affiliateEmail}`,
  }
  console.log('updating', affiliateEmail, 'to', data.email)
  await axios.put(`https://api.getrewardful.com/v1/affiliates/${affiliateId}`, data, {
    auth: {
      username: REWARDFUL_API_KEY,
      password: '',
    },
  })
}

const createRewardfulAffiliate = async (api_secret, data) => {
  try {
    const result = await axios.post(`https://api.getrewardful.com/v1/affiliates`, data, {
      auth: {
        username: api_secret,
        password: '',
      },
    })
    return result.data
  } catch (err) {
    console.log(err?.response?.data)
    throw err
  }
}

const processUsers = async () => {
  let users
  try {
    await fs.access(USERS_FILE_PATH)
    console.log('Users file found. Reading users from file...')
    users = JSON.parse(await fs.readFile(USERS_FILE_PATH, 'utf8'))
  } catch (error) {
    console.log('Users file not found. Fetching users from Rewardful API...')
    users = await fetchRewardfulUsers()
  }

  let processedCount = 0

  for (const user of users) {
    if (!user.stripe_customer_id) {
      console.log(`Skipping user ${user.id} due to null stripe_customer_id.`)
      continue
    }

    // Fetch all memberships where the user is an owner
    const memberships = await prisma.membership.findMany({
      where: {
        users: {
          some: {
            email: user.email,
            role: 'owner',
          },
        },
      },
      include: {
        membership_setting: true,
      },
    })

    // Check if any of these memberships are inactive
    const hasInactiveMembership = memberships.some((membership) => !membership.active)
    /* check if it has more than 1 active community being an owner */

    if (hasInactiveMembership) {
      console.log(`Marking email as deprecated for user ${user.id} with email ${user.email}`)
      try {
        await updateEmailToDeprecated(user.id, user.email)
        console.log(`Marked email as deprecated for user ${user.id}.`)
        processedCount++
      } catch (error) {
        console.log(`Failed to mark email as deprecated for user ${user.id}:`, error.message)
      }

      console.log(`User ${user.id} has more than one active community. Skipping.`)
      /* create rewardful user with original email and assign to all active commnuities to the affiliate field the data of the creation of the affiliate*/
      try {
        const affiliateData = {
          first_name: user.first_name || '_',
          last_name: user.last_name || '_',
          email: user.email,
          stripe_customer_id: user.stripe_customer_id,
        }
        console.log(`Creating new affiliate for user ${user.id} with email ${user.email}`)
        const newAffiliate = await createRewardfulAffiliate(REWARDFUL_API_KEY, affiliateData)
        console.log(`New affiliate created for user ${user.id} with email ${user.email}`)
        for (const membership of memberships) {
          if (membership.active) {
            await prisma.membershipSetting.update({
              where: { membership_id: membership.id },
              data: { affiliate: newAffiliate },
            })
            console.log(`New affiliate assigned to community ${membership.slug}`)
          }
        }
      } catch (error) {
        console.log(`Failed to create new affiliate for user ${user.id}:`, error.message)
      }
    } else {
      console.log(
        `No inactive memberships found for user ${user.id} with email ${user.email}. Copying affiliate config from the one that has a non null value to the rest.`,
      )
      const membershipWithAffiliateData = memberships.find((membership) => membership.membership_setting.affiliate)
      if (membershipWithAffiliateData) {
        for (const membership of memberships) {
          if (!membership.membership_setting.affiliate) {
            console.log(`Copying affiliate config to community ${membership.slug}`)
            await prisma.membershipSetting.update({
              where: { membership_id: membership.id },
              data: { affiliate: membershipWithAffiliateData.membership_setting.affiliate },
            })
          }
        }
      } else {
        console.log(`No affiliate found for user ${user.id} with email ${user.email}. Skipping.`)
      }
    }
  }

  console.log(`Processing completed. Total emails marked as deprecated: ${processedCount}`)
}

processUsers().catch(console.error)
/*
REWARDFUL_API_KEY=your_production_api_key doppler run -- node apps/memberup/scripts/rewardly-delete-users-inactive-communities.js
*/
