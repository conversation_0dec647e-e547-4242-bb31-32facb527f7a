const algoliasearch = require('algoliasearch')
const { PrismaClient } = require('@prisma/client')
const axios = require('axios')
const prisma = new PrismaClient()
const algoliaClient = algoliasearch(
  process.env.NEXT_PUBLIC_ALGOLIA_APP_ID,
  process.env.NEXT_PUBLIC_ALGOLIA_ADMIN_API_KEY,
)
const algoliaIndex = algoliaClient.initIndex('content-library')

const stripHtml = (htmlString) => {
  return htmlString.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ')
}

async function getThumbnail(playbackId) {
  if (!playbackId) return null
  const url = `https://image.mux.com/${playbackId}/thumbnail.jpg`
  const response = await axios.get(url, { responseType: 'stream' })
  return response.data.responseUrl
}

async function reindexContentLibrary() {
  try {
    console.log('Starting reindexing process...')
    await algoliaIndex.clearObjects().wait()
    console.log('Cleared existing objects from Algolia index.')

    console.log('Fetching contentLibraryCourseLessons from database...')
    const lessons = await prisma.contentLibraryCourseLesson.findMany()
    console.log(`Fetched ${lessons.length} lessons from database.`)

    console.log('Creating Algolia objects...')
    let algoliaObjects = []
    for (let i = 0; i < lessons.length; i++) {
      let lesson = lessons[i]
      console.log(`Processing lesson ${i + 1} of ${lessons.length}`)

      let thumbnail = null
      if (!lesson.content_library_course_id) {
        console.log('No course id set in lesson, skipping lesson id ', lesson.id)
        continue
      }

      const relatedCourse = await prisma.contentLibraryCourse.findUnique({
        where: {
          id: lesson.content_library_course_id,
        },
      })

      if (lesson.thumbnail_url) {
        thumbnail = lesson.thumbnail_url
      } else if (lesson.type === 'video' && lesson.media_file?.playback_ids?.[0]?.id) {
        console.log('Fetching thumbnail from Mux...', lesson.media_file.playback_ids[0].id)
        try {
          thumbnail = await getThumbnail(lesson.media_file.playback_ids[0].id)
        } catch (error) {
          thumbnail = relatedCourse?.thumbnail
        }

        /* update lesson thumbnail on model */
        await prisma.contentLibraryCourseLesson.update({
          where: {
            id: lesson.id,
          },
          data: {
            thumbnail_url: thumbnail,
          },
        })
      } else {
        thumbnail = relatedCourse?.thumbnail
      }

      algoliaObjects.push({
        title: lesson.title,
        visibility: lesson.visibility,
        type: lesson.type,
        text: stripHtml(lesson.text || '').trim(),
        thumbnail_url: thumbnail,
        objectID: lesson.id,
        course_id: lesson.content_library_course_id,
        viewable_by: lesson.membership_id,
      })
    }
    console.log(`Created ${algoliaObjects.length} Algolia objects.`)

    console.log('Uploading Algolia objects...')
    const responses = await algoliaIndex.saveObjects(algoliaObjects).wait()
    console.log(`Uploaded ${algoliaObjects.length} lessons to Algolia.`)
  } catch (error) {
    console.error('Error reindexing content library:', error)
  } finally {
    await prisma.$disconnect()
    console.log('Disconnected from Prisma.')
  }
}

reindexContentLibrary()

/* 

doppler run -- node apps/memberup/scripts/algolia-content-library-reindex.js

*/
