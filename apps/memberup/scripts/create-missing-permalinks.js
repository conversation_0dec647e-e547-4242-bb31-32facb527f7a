const { askQuestion, getStreamApps } = require('./common')
const { StreamChat } = require('stream-chat')
const { PrismaClient } = require('@prisma/client')
const { slugify } = require('@memberup/shared/src/libs/string-utils')

const isUUIDv4 = (uuid) => {
  // Regular expression pattern for UUID v4
  const uuidv4Pattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  // Test if the provided string matches the UUID v4 pattern
  return uuidv4Pattern.test(uuid)
}

const prisma = new PrismaClient({
  log: [
    {
      emit: 'event',
      level: 'query',
    },
  ],
})

prisma.$on('query', (e) => {
  //console.log(e.query, e.params)
})

async function getLatestPostSlug(slug) {
  const prefix = `${slug}-`
  const feeds = await prisma.feed.findMany({
    where: {
      permalink: {
        startsWith: prefix,
      },
    },
    orderBy: [
      {
        createdAt: 'desc',
      },
      { id: 'desc' },
    ],
    select: {
      permalink: true,
    },
  })
  const permalinks = feeds.map((f) => f.permalink)
  const regex = new RegExp(`^${prefix}\\d+$`)
  const potentialPermalinks = permalinks.filter((p) => regex.test(p))
  return potentialPermalinks[0]
}

const generateNextSlug = (permalink, baseSlug) => {
  let currentSeq = 0
  if (permalink) {
    const parts = permalink.split('-')
    currentSeq = parseInt(parts[parts.length - 1])
    if (isNaN(currentSeq)) {
      currentSeq = 0
    }
  }
  const nextSeq = currentSeq + 1
  return `${baseSlug}-${nextSeq}`
}

async function generatePermalink(post) {
  let baseSlug = slugify(post.title)
  let latestFeedSlug = await getLatestPostSlug(baseSlug)
  // NOTE: We pass baseSlug here as the matching of string with Prisma is not perfect.
  return generateNextSlug(latestFeedSlug, baseSlug)
}

const main = async () => {
  const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
  const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET
  const DATABASE_URL = process.env.DATABASE_URL

  console.log(`Creating missing permalinks.`)
  console.log(`Using Database URL ${DATABASE_URL}`)
  console.log(`Using GetStream App: key=${GET_STREAM_APP_KEY}, name=${getStreamApps[GET_STREAM_APP_KEY].name}`)

  const answer = await askQuestion('Do you want to proceed? (y/n): ')
  if (answer.toLowerCase() !== 'y') {
    console.log('Good bye.')
    return
  }

  const client = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
    timeout: 30000,
  })

  await prisma.feed.updateMany({
    data: {
      permalink: null,
    },
  })

  const posts = await prisma.feed.findMany({
    where: {
      permalink: null,
      feed_type: 'default',
      title: {
        not: {
          equals: null,
        },
      },
    },
    select: {
      id: true,
      title: true,
      user_id: true,
    },
    orderBy: [
      {
        createdAt: 'asc',
      },
      { id: 'desc' },
    ],
  })

  console.log(`Creating permalinks for ${posts.length} posts.`)

  let j = 0
  for (let post of posts) {
    j++
    console.log(`Processing post ${j} of ${posts.length}`)
    if (isUUIDv4(post.title)) {
      console.log(`Ignoring post ${post.id} because it has an UUID as the title.`)
      continue
    }

    const permalink = await generatePermalink(post)

    await prisma.feed.update({
      where: {
        id: post.id,
      },
      data: {
        permalink: permalink,
      },
    })

    try {
      const upsertParentData = {
        permalink: permalink,
      }
      const updatedParentMessage = await client.partialUpdateMessage(
        post.id,
        {
          set: upsertParentData,
          unset: [],
        },
        post.user_id,
      )
    } catch (e) {
      console.log(`Error: ${e.message}`)
    }

    console.log(`permalink: ${permalink} id: ${post.id}`)
  }
}

main()
