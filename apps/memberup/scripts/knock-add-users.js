// doppler run -- node  apps/memberup/scripts/knock-add-users.js --user-ids

import 'dotenv/config'

import { PrismaClient } from '@prisma/client'
import { hideBin } from 'yargs/helpers'
import yargs from 'yargs/yargs'

import { knockIdentifyUser } from '../../../packages/shared/src/libs/knock.ts'
import { confirmScriptExecution } from './common.js'

const prisma = new PrismaClient()

const argv = yargs(hideBin(process.argv)).option('user-ids', {
  type: 'string',
  description: 'Comma-separated list of users',
  coerce: (arg) => (arg ? arg.split(',') : undefined),
}).argv

// Delay function
const delay = (ms) => new Promise((res) => setTimeout(res, ms))

// Helper function to format milliseconds into MM:SS
function formatTime(ms) {
  if (ms < 0) ms = 0
  const totalSeconds = Math.floor(ms / 1000)
  const minutes = Math.floor(totalSeconds / 60)
  const seconds = totalSeconds % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
}

async function main() {
  const shouldProceed = await confirmScriptExecution()

  if (!shouldProceed) return

  const userIds = argv['user-ids']

  const users = await prisma.user.findMany({
    where:
      userIds && userIds.length > 0
        ? {
            id: {
              in: userIds,
            },
          }
        : {},
    select: {
      id: true,
      first_name: true,
      last_name: true,
      email: true,
      role: true,
      profile: {
        select: {
          image: true,
          image_crop_area: true,
        },
      },
    },
  })

  const totalUsers = users.length
  if (totalUsers === 0) {
    console.log('No users to process.')
    return
  }
  console.log(`Found ${totalUsers} user(s) to process.`)

  let processedCount = 0
  const startTime = Date.now()

  for (const user of users) {
    // console.log(`Processing user ${user.id}, ${user.email}`)
    await knockIdentifyUser(user)
    processedCount++

    const elapsedTime = Date.now() - startTime
    const averageTimePerUser = elapsedTime / processedCount
    const remainingUsers = totalUsers - processedCount
    const estimatedTimeRemaining = remainingUsers * averageTimePerUser

    const etrString = formatTime(estimatedTimeRemaining)
    const progressPercent = ((processedCount / totalUsers) * 100).toFixed(2)

    // Define column widths
    const progressColWidth = 30
    const emailColWidth = 40
    const etrColWidth = 15

    const progressText = `User ${processedCount}/${totalUsers} (${progressPercent}%)`
    const emailText =
      user.email.length > emailColWidth ? user.email.substring(0, emailColWidth - 3) + '...' : user.email
    const etrText = `ETR: ${etrString}`

    process.stdout.write(
      `\n${progressText.padEnd(progressColWidth)} | ${emailText.padEnd(emailColWidth)} | ${etrText.padEnd(etrColWidth)}`,
    )
  }

  if (totalUsers > 0) {
    process.stdout.write('\n') // Move to the next line after completion
  }
  console.log('All users processed.')
}

main()
