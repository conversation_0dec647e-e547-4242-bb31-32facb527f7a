require('dotenv').config()
const { PrismaClient } = require('@prisma/client')
const { Knock } = require('@knocklabs/node')
const readline = require('readline')

const prisma = new PrismaClient()
const KNOCK_API_KEY = process.env.KNOCK_SECRET_API_KEY
const knockClient = new Knock(KNOCK_API_KEY)

const DELAY_TIME = 1000 // Delay to prevent rate limiting

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
})

const delay = (ms) => new Promise((res) => setTimeout(res, ms))

const deleteUsersFromKnock = async (userIds) => {
  for (const userId of userIds) {
    try {
      await knockClient.users.delete(userId)
      console.log(`Deleted user from Knock: ${userId}`)
      await delay(DELAY_TIME)
    } catch (error) {
      console.error(`Failed to delete user ${userId} from <PERSON>nock: ${error.message}`)
    }
  }
}

const getInactiveUserIds = async () => {
  const users = await prisma.user.findMany({
    where: {
      status: {
        not: 'active',
      },
      role: {
        in: ['member', 'owner', 'creator'],
      },
    },
    select: {
      id: true,
    },
  })
  return users.map((user) => user.id)
}

const askQuestion = (query) => new Promise((resolve) => rl.question(query, resolve))

const main = async () => {
  try {
    const inactiveUserIds = await getInactiveUserIds()
    console.log(`Found ${inactiveUserIds.length} inactive users to delete from Knock.`)

    const answer = await askQuestion(
      `You are about to delete ${inactiveUserIds.length} inactive users from Knock. Yes or no to proceed? `,
    )
    if (answer.toLowerCase() !== 'yes') {
      console.log('Operation cancelled.')
      rl.close()
      return
    }

    await deleteUsersFromKnock(inactiveUserIds)
    console.log('Completed deleting inactive users from Knock.')
  } catch (error) {
    console.error(`An error occurred: ${error.message}`)
  } finally {
    rl.close()
  }
}

main()

// doppler run -- node apps/memberup/scripts/knock-delete-inactive-users.js
