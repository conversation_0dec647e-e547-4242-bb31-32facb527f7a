const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function migrateData() {
  try {
    // Fetch all data from old models
    const memberships = await prisma.membership.findMany()
    const libraries = await prisma.library.findMany()
    const librarySequences = await prisma.librarySequence.findMany()
    const libraryCategories = await prisma.libraryCategory.findMany()

    let processedMemberships = 0
    let modifiedMemberships = 0
    let unmodifiedMemberships = 0

    // Loop through each membership
    for (const membership of memberships) {
      let membershipModified = false

      // Find the corresponding library for the membership
      const library = libraries.find((lib) => lib.membership_id === membership.id)

      // If a library exists, migrate the data
      if (library) {
        const contentLibrary = await prisma.contentLibrary.create({
          data: {
            title: library.title,
            description: library.description,
            metadata: {}, // Initialize metadata
            createdAt: library.createdAt,
            updatedAt: library.updatedAt,
            membership_id: library.membership_id,
          },
        })

        const contentLibraryCourse = await prisma.contentLibraryCourse.create({
          data: {
            title: library.title,
            description: library.description,
            contentLibrary: {
              connect: { id: contentLibrary.id },
            },
            createdAt: library.createdAt,
            updatedAt: library.updatedAt,
            visibility: library.status === 'published' ? 'published' : 'draft',
            membership_id: library.membership_id,
            content_library_id: contentLibrary.id,
          },
        })

        // Find related librarySequences and create LibraryCourses
        const relatedSequences = librarySequences.filter((seq) => seq.library_id === library.id)
        for (const sequence of relatedSequences) {
          const relatedCategories = libraryCategories.filter((cat) => cat.library_id === library.id)
          for (const category of relatedCategories) {
            const contentLibraryCourseSection = await prisma.contentLibraryCourseSection.create({
              data: {
                name: category.name,
                sequence: sequence.sequence,
                contentLibraryCourse: {
                  connect: { id: contentLibraryCourse.id },
                },
                createdAt: category.createdAt,
                updatedAt: category.updatedAt,
                membership_id: category.membership_id,
                content_library_id: contentLibrary.id,
                content_library_course_id: contentLibraryCourse.id,
              },
            })

            // Create LibraryCourseLessons for each section
            for (const lesson of category.lessons) {
              await prisma.contentLibraryCourseLesson.create({
                data: {
                  title: lesson.title,
                  text: lesson.text,
                  type: 'video', // Set type as 'video'
                  media_file: library.mux_asset, // Set media_file from library.mux_asset
                  contentLibraryCourseSection: {
                    connect: { id: contentLibraryCourseSection.id },
                  },
                  sequence: sequence.sequence,
                  createdAt: library.createdAt,
                  updatedAt: library.updatedAt,
                  visibility: library.status === 'published' ? 'published' : 'draft',
                  membership_id: library.membership_id,
                  section_id: contentLibraryCourseSection.id,
                  content_library_id: contentLibrary.id,
                  content_library_course_id: contentLibraryCourse.id,
                },
              })
            }
          }
        }

        // Update the metadata of the ContentLibrary to include the course_order
        await prisma.contentLibrary.update({
          where: { id: contentLibrary.id },
          data: {
            metadata: {
              course_order: [contentLibraryCourse.id],
            },
          },
        })

        membershipModified = true
      } else {
        // If no library exists, create an empty ContentLibrary, ContentLibraryCourse, ContentLibraryCourseSection, and ContentLibraryCourseLesson
        const contentLibrary = await prisma.contentLibrary.create({
          data: {
            title: 'Default',
            description: 'Default',
            membership_id: membership.id,
          },
        })

        const contentLibraryCourse = await prisma.contentLibraryCourse.create({
          data: {
            title: 'Default',
            description: 'Default',
            visibility: 'draft',
            membership_id: membership.id,
            content_library_id: contentLibrary.id,
          },
        })

        const contentLibraryCourseSection = await prisma.contentLibraryCourseSection.create({
          data: {
            name: 'Default',
            contentLibraryCourse: {
              connect: { id: contentLibraryCourse.id },
            },
            membership_id: membership.id,
            content_library_id: contentLibrary.id,
            content_library_course_id: contentLibraryCourse.id,
          },
        })

        await prisma.contentLibraryCourseLesson.create({
          data: {
            title: 'Default',
            text: 'Default',
            type: 'text',
            contentLibraryCourseSection: {
              connect: { id: contentLibraryCourseSection.id },
            },
            membership_id: membership.id,
            section_id: contentLibraryCourseSection.id,
            content_library_id: contentLibrary.id,
            content_library_course_id: contentLibraryCourse.id,
          },
        })

        // Update the metadata of the ContentLibrary to include the course_order
        await prisma.contentLibrary.update({
          where: { id: contentLibrary.id },
          data: {
            metadata: {
              course_order: [contentLibraryCourse.id],
            },
          },
        })

        membershipModified = true
      }

      processedMemberships++
      membershipModified ? modifiedMemberships++ : unmodifiedMemberships++
      console.log(
        `Processed ${processedMemberships} out of ${memberships.length} memberships. Progress: ${Math.round(
          (processedMemberships / memberships.length) * 100,
        )}% Membership ID: ${membership.id}  Membership Modified: ${membershipModified}`,
      )
    }

    console.log(`Total memberships processed: ${processedMemberships}`)
    console.log(`Memberships modified: ${modifiedMemberships}`)
    console.log(`Memberships unmodified: ${unmodifiedMemberships}`)
  } catch (error) {
    console.error('Error migrating data:', error)
  } finally {
    await prisma.$disconnect()
    console.log('Disconnected from Prisma.')
  }
}

migrateData()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

/* 

doppler run -- node apps/memberup/scripts/create-default-libraries.js

*/
