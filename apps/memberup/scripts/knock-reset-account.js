// Deletes all users, tenants and objects for all communities
// Add --yes parameter for unattended operation
//
// Ensure Doppler is set up to run on the staging environment
// doppler run -- node  apps/memberup/scripts/knock-reset-account.js

require('dotenv').config()

const { askQuestion, getKnockApps } = require('./common')

const { Knock } = require('@knocklabs/node')
const { PrismaClient } = require('@prisma/client')
const yargs = require('yargs/yargs')
const { hideBin } = require('yargs/helpers')

const prisma = new PrismaClient()

const KNOCK_API_KEY = process.env.KNOCK_SECRET_API_KEY
const NEXT_PUBLIC_ENV = process.env.NEXT_PUBLIC_ENV

const argv = yargs(hideBin(process.argv))
  .option('community-slugs', {
    type: 'string',
    description: 'Comma-separated list of community slugs',
    coerce: (arg) => (arg ? arg.split(',') : []),
  })
  .option('yes', {
    type: 'boolean',
    description: 'Unattended operation',
  }).argv

// Delay function
const delay = (ms) => new Promise((res) => setTimeout(res, ms))

const getMembershipsBySlugs = async (communitySlugs) => {
  console.log('Fetching membership from database...', communitySlugs)
  const memberships = await prisma.membership.findMany({
    where: {
      slug: {
        in: communitySlugs?.length ? communitySlugs : undefined,
      },
    },
    select: {
      id: true,
      slug: true,
      name: true,
    },
  })
  return memberships
}

const resetAccount = async (communitySlugs) => {
  const memberships = await getMembershipsBySlugs(communitySlugs)

  const knockClient = new Knock(KNOCK_API_KEY)

  // Delete all users
  let nextPage = null

  let deletedUsersCount = 0
  let totalUsersCount = 0

  do {
    const userPage = await knockClient.users.list({ after: nextPage })
    const userIds = userPage.entries.map((u) => u.id)

    if (userIds.length > 0) {
      await knockClient.users.bulkDelete(userIds) // Max 1 request per second
      await delay(1000)
    }
    deletedUsersCount += userIds.length

    if (nextPage === null) {
      totalUsersCount = userPage.page_info.total_count
    }

    if (totalUsersCount === 0) {
      console.log('No users to delete')
    } else {
      console.log(`Deleted ${deletedUsersCount} of ${totalUsersCount} users`)
    }

    nextPage = userPage.page_info.after
  } while (nextPage)

  // Delete all tenants
  nextPage = null

  let deletedTenantsCount = 0
  let totalTenantsCount = 0

  do {
    const tenantPage = await knockClient.tenants.list({ after: nextPage })
    const tenantIds = tenantPage.entries.map((u) => u.id)

    await delay(1000)

    if (nextPage === null) {
      totalTenantsCount = tenantPage.page_info.total_count

      if (totalTenantsCount === 0) {
        console.log('No tenants to delete')
      }
    }

    if (tenantIds.length > 0) {
      for (const tenantId of tenantIds) {
        await knockClient.tenants.delete(tenantId) // Max 5 requests per second
        await delay(200)

        deletedTenantsCount++
        console.log(`Deleted ${deletedTenantsCount} of ${totalTenantsCount} tenants`)
      }
    }

    nextPage = tenantPage.page_info.after
  } while (nextPage)

  // Delete all objects
  let membershipNumber = 1

  for (let membership of memberships) {
    const objectsPage = await knockClient.objects.list(membership.slug)
    const objectIds = objectsPage.entries.map((u) => u.id)
    if (objectIds.length > 0) {
      await delay(1000)
      await knockClient.objects.bulkDelete(membership.slug, objectIds) // Max 1 request per second
    }
    console.log(
      `Deleted ${objectIds.length} objects for community ${membership.slug} - ${membershipNumber} of ${memberships.length} communities`,
    )
    membershipNumber++
  }
  console.log('====== Knock account has been fully reset =======')
}

async function main() {
  const communitySlugs = argv['community-slugs']
  const yes = argv['yes']

  if (NEXT_PUBLIC_ENV !== 'staging') {
    console.log('This script can only be run in the staging environment')
    process.exit(-1)
  }

  console.log(`Resetting knock account, removing all users, tenants and objects for environment ${NEXT_PUBLIC_ENV}\n`)

  console.log(`Using KNOCK_API_KEY key=${KNOCK_API_KEY}, name=${getKnockApps[KNOCK_API_KEY].name}\n`)

  if (!yes) {
    const answer = await askQuestion('Do you want to proceed? (y/n): ')

    if (answer.toLowerCase() !== 'y') {
      console.log('Good bye.')
      return
    }
  }

  resetAccount(communitySlugs)
}

main()
