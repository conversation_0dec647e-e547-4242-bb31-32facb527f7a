require('dotenv').config()
const { askQuestion, getStreamApps } = require('./common')

const { StreamChat } = require('stream-chat')

const { Prisma, PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET
const DATABASE_URL = process.env.DATABASE_URL

async function main() {
  console.log(`Using Database URL ${DATABASE_URL}`)
  console.log(`Using GetStream App: key=${GET_STREAM_APP_KEY}, name=${getStreamApps[GET_STREAM_APP_KEY].name}`)

  const answer = await askQuestion('Do you want to proceed? (y/n): ')
  if (answer.toLowerCase() !== 'y') {
    console.log('Good bye.')
    return
  }

  const selectedReactions = await prisma.$queryRaw(Prisma.sql`SELECT 
  MIN(id) AS id,
  MIN(enforce_unique) AS enforce_unique,
  type,
  MIN(text) AS text,
  MIN(createdAt) AS createdAt,
  MIN(updatedAt) AS updatedAt,
  message_id,
  user_id,
  MIN(metadata) AS metadata
FROM 
  reactions
GROUP BY
  type, user_id, message_id;`)

  const idsToKeep = selectedReactions.map((r) => r.id)
  const idsToDelete = await prisma.reaction.findMany({
    where: {
      id: {
        notIn: idsToKeep,
      },
    },
  })

  const transaction = await prisma.$transaction([
    prisma.reaction.deleteMany({
      where: {
        id: {
          notIn: idsToKeep,
        },
      },
    }),
  ])
  console.log('Transaction complete:', transaction)
  console.log('Deleted Reaction Ids:', JSON.stringify(idsToDelete))

  const client = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
    timeout: 30000,
  })
}

main()
