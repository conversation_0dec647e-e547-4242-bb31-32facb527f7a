require('dotenv').config()
const { askQuestion, getStreamApps } = require('./common')

const { StreamChat } = require('stream-chat')

const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY
const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET
const DATABASE_URL = process.env.DATABASE_URL

async function main() {
  console.log(`Using Database URL ${DATABASE_URL}`)
  console.log(`Using GetStream App: key=${GET_STREAM_APP_KEY}, name=${getStreamApps[GET_STREAM_APP_KEY].name}`)

  const answer = await askQuestion('Do you want to proceed? (y/n): ')
  if (answer.toLowerCase() !== 'y') {
    console.log('Good bye.')
    return
  }

  const params = process.argv.slice(2)

  let [ignoreUniqueConstraintErrors, ignoreGetStreamDuplicateErrors] = params
  console.log(
    `ignoreUniqueConstraintErrors: ${ignoreUniqueConstraintErrors}, ignoreGetStreamDuplicateErrors: ${ignoreGetStreamDuplicateErrors}`,
  )

  const client = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
    timeout: 30000,
  })

  const replies = await prisma.reaction.findMany({
    where: {
      type: {
        contains: 'reply',
      },
      //id: 'bd189018-3de7-41de-892a-2e0334821b70',
      //id: '5b3a0b1f-0743-49c8-aca2-176de9bc0401',
    },
    orderBy: [{ message_id: 'desc' }, { createdAt: 'asc' }],
    //take: 1,
  })

  //const channel = client.getChannelById()
  const channelsNotFound = []
  let j = 0
  for (let reply of replies) {
    j++
    console.log(`Processing reply ${j} of ${replies.length}`)
    try {
      const parentComment = await prisma.feed.findFirst({ where: { id: reply['message_id'] } })
      if (!parentComment) {
        console.error(`Error: Reply ${reply.id}: Parent comment ${reply['message_id']} not found`)
        continue
      }

      if (channelsNotFound.includes(parentComment.channel_id)) {
        console.error(`Error: Reply ${reply.id}: Channel ${parentComment.channel_id} not found already`)
        continue
      }

      const filter = { id: parentComment.channel_id }
      const channels = await client.queryChannels(filter, {})
      if (channels.length === 0) {
        console.error(`Error: Reply ${reply.id}: Channel ${parentComment.channel_id} not found`)
        channelsNotFound.push(parentComment.channel_id)
        continue
      }

      if (!parentComment.parent_id) {
        console.error(`Error: Reply ${reply.id}: Parent Id for Comment ${parentComment.id} is null`)
        continue
      }

      const newReply = {
        id: reply.id,
        feed_type: 'comment',
        feed_status: 'active',
        text: reply.text,
        html: null,
        attachments: null,
        metadata: { reaction: reply }, // Just save some information to know that we converted from a reaction
        permalink: null,
        reports: null,
        title: reply.id,
        createdAt: reply.createdAt,
        updatedAt: reply.updatedAt,
        user_id: reply.user_id,
        channel_id: parentComment.channel_id,
        parent_id: parentComment.parent_id,
        reply_parent_id: parentComment.id,
        hierarchy_order: null,
      }

      try {
        await prisma.feed.create({ data: newReply })
      } catch (e) {
        if (!ignoreUniqueConstraintErrors) {
          throw e
        }
      }

      const newStreamComment = {
        id: newReply.id,
        feed_type: newReply.feed_type,
        feed_status: newReply.feed_status,
        created_at_ext: newReply.createdAt, // We use this field as the updated_at is a reserved field. We will need to render in the UI giving priority to this.
        title: newReply.title,
        text: newReply.text,
        user_id: newReply.user_id,
        parent_id: newReply.parent_id,
        reply_parent_id: newReply.reply_parent_id,
        metadata: newReply.metadata,
      }

      const channel = client.channel('team', parentComment.channel_id)

      // Update the parent post
      const streamParentPostResult = await client.getMessage(newReply.parent_id)
      if (!streamParentPostResult) {
        console.error(`Error for reply ${reply.id}: cannot find the stream post on Getstream.`)
        continue
      }

      // Create the comment
      try {
        await channel.sendMessage(newStreamComment)
      } catch (e) {
        if (!ignoreGetStreamDuplicateErrors) {
          throw e
        }
      }

      const streamParentPost = streamParentPostResult['message']
      const featuredCommenters = streamParentPost['featured_commenters'] || []

      const removeByIdAndSelectLastEntries = (arr, id) => {
        const index = arr.findIndex((obj) => obj.id === id)
        if (index !== -1) {
          arr.splice(index, 1)
        }
        const lastThree = arr.slice(-5)
        return lastThree
      }

      const updatedFeaturedCommenters = removeByIdAndSelectLastEntries(featuredCommenters, newReply.user_id)

      const user = await prisma.user.findUnique({
        where: {
          id: newReply.user_id,
        },
        include: {
          profile: true,
        },
      })

      updatedFeaturedCommenters.push({
        id: user.id,
        name: `${user.first_name} ${user.last_name}`,
        src: user.profile.image,
      })

      const upsertParentData = {}
      upsertParentData['featured_commenters'] = updatedFeaturedCommenters
      upsertParentData['latest_comment_timestamp'] = Math.round(new Date(newReply.createdAt / 1000))

      const updatedParentMessage = await client.partialUpdateMessage(
        streamParentPost.id,
        {
          set: upsertParentData,
          unset: [],
        },
        user.id, /// TODO: Should we use this user or the actual owner of the post?
      )
      console.log(`Processed reply: ${reply.id}`)
    } catch (e) {
      if (e.code === 'P2002') {
        console.error(`Error for reply ${reply.id}: unique constraint violation, comment already exist.`)
      } else {
        console.error(`Error for reply ${reply.id}: `, e.message)
      }
    }
  }

  // NOTE: We don't need to delete reply reactions as they are not shown in the app anymore.
}

main()
