// Ensure Doppler is set up to run on the staging environment
// doppler run -- node  apps/memberup/scripts/update-action-names.js

require('dotenv').config()
const { askQuestion } = require('./common')
const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()
const DATABASE_URL = process.env.DATABASE_URL

const updateActivityNames = async () => {
  console.log(`Using Database URL ${DATABASE_URL}`)

  const answer = await askQuestion('Do you want to proceed? (y/n): ')

  if (answer.toLowerCase() !== 'y') {
    console.log('Good bye.')
    return
  }

  const likeUnlike = await prisma.action.findFirst({
    where: {
      action_name: 'LIKE_UNLIKE',
    },
  })

  if (likeUnlike) {
    await prisma.action.update({
      where: {
        action_name: 'LIKE_UNLIKE',
      },
      data: {
        action_name: '<PERSON>I<PERSON>',
      },
    })
  }

  await prisma.action.upsert({
    where: {
      action_name: 'USER_ACTIVE',
    },
    create: {
      action_name: 'USER_ACTIVE',
      points: 1,
    },
    update: {},
  })

  await prisma.action.upsert({
    where: {
      action_name: 'POST_CREATED',
    },
    create: {
      action_name: 'POST_CREATED',
      points: 1,
    },
    update: {},
  })

  await prisma.action.upsert({
    where: {
      action_name: 'COMMENT_CREATED',
    },
    create: {
      action_name: 'COMMENT_CREATED',
      points: 1,
    },
    update: {},
  })

  await prisma.action.upsert({
    where: {
      action_name: 'LESSON_COMPLETED',
    },
    create: {
      action_name: 'LESSON_COMPLETED',
      points: 1,
    },
    update: {},
  })

  console.log('Actions created/updated successfully')
}

updateActivityNames()
