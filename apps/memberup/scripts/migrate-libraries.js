const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()
const axios = require('axios')

let lessonCount = 0
let defaultLessonCount = 0

const getFileType = (fileType) => {
  if (!fileType) return 'other'
  if (fileType.includes('video')) return 'video'
  if (fileType.includes('image')) return 'image'
  if (fileType.includes('audio')) return 'audio'
  if (fileType.includes('pdf')) return 'pdf'
  if (fileType.includes('doc')) return 'doc'
  if (fileType.includes('ppt')) return 'ppt'
  if (fileType.includes('epub')) return 'epub'
  return 'other'
}

async function migrateData() {
  try {
    await prisma.contentLibrary.deleteMany()
    // Fetch all data from old models
    const memberships = await prisma.membership.findMany()
    const libraries = await prisma.library.findMany()
    const librarySequences = await prisma.librarySequence.findMany()
    const libraryCategories = await prisma.libraryCategory.findMany()

    let processedMemberships = 0
    let modifiedMemberships = 0
    let unmodifiedMemberships = 0

    // Loop through each membership
    for (const membership of memberships) {
      let membershipModified = false

      // Create a ContentLibrary for each membership
      const contentLibrary = await prisma.contentLibrary.create({
        data: {
          title: 'Content Library',
          description: '',
          metadata: {}, // Initialize metadata
          membership_id: membership.id,
        },
      })

      // Create a ContentLibraryCourse for each membership
      const contentLibraryCourse = await prisma.contentLibraryCourse.create({
        data: {
          title: 'Course',
          description: '',
          content_library_id: contentLibrary.id,
          visibility: 'published',
          membership_id: membership.id,
        },
      })

      await prisma.contentLibrary.update({
        where: {
          id: contentLibrary.id,
        },
        data: {
          metadata: {
            course_order: [contentLibraryCourse.id],
          },
        },
      })

      // Find related libraryCategories and create LibraryCourseSections for each category
      const relatedCategories = libraryCategories.filter((cat) => cat.membership_id === membership.id)

      if (relatedCategories.length > 0) {
        for (const category of relatedCategories) {
          const contentLibraryCourseSection = await prisma.contentLibraryCourseSection.create({
            data: {
              name: category.name,
              sequence: category.sequence,
              content_library_course_id: contentLibraryCourse.id,
              createdAt: category.createdAt,
              updatedAt: category.updatedAt,
              membership_id: membership.id,
              content_library_id: contentLibrary.id,
              visibility: 'published',
            },
          })

          // Find the corresponding libraries for the membership and the category
          const relatedLibraries = libraries.filter(
            (lib) => lib.membership_id === membership.id && lib.categories.includes(category.id),
          )

          // If libraries exist, migrate the data
          if (relatedLibraries.length > 0) {
            for (const library of relatedLibraries) {
              // Check if the library is associated with the category
              const librarySequence = librarySequences.find(
                (seq) => seq.library_id === library.id && seq.library_category_id === category.id,
              )

              if (librarySequence) {
                // Find the sequence for the library and the category
                const sequence = librarySequence.sequence || 0

                /* for library.assets, I need to replace each asset 
                original format: {"url": "https://res.cloudinary.com/memberup-llc/image/upload/v1695787837/blob_kvhi15.jpg", "name": "IMG_8955.jpg", "size": 1159106, "type": "image/jpeg"}
                new format: {"url": "https://res.cloudinary.com/memberup-llc/image/upload/v1700766368/blob_gfhjhe.jpg", "filename": "hawaii.jpeg", "mimetype": "image", "public_id": "blob_gfhjhe", "passthrough": "library:1696f01b-7985-437a-af78-56deb063a0a5", "size_in_bytes": 161317, "uploaded_date": "Thu, Nov 23, 2023 4:06 PM"}
                */

                // Replace the assets with the new format
                const assets = library.assets?.map((asset) => {
                  const newAsset = {
                    ...asset,
                    filename: asset.name,
                    mimetype: getFileType(asset.type),
                    size_in_bytes: asset.size,
                  }

                  return newAsset
                })

                const isPDFTypeLesson = library.mimetype === 'pdf'
                if (isPDFTypeLesson) {
                  assets.push({
                    url: library.url,
                    filename: library.filename,
                    mimetype: library.mimetype,
                    uploaded_date: library.createdAt,
                    size_in_bytes: assets?.[0]?.size ?? null,
                  })
                }

                await prisma.contentLibraryCourseLesson.create({
                  data: {
                    title: library.title ?? 'Lesson',
                    text: library.description ?? '',
                    type: library.mux_asset ? 'video' : 'text',
                    media_file: library.mux_asset,
                    thumbnail_url: library.thumbnails,
                    sequence: sequence, // Use the sequence for the library and the category
                    createdAt: library.createdAt,
                    updatedAt: library.updatedAt,
                    visibility: library.status === 'published' ? 'published' : 'draft',
                    membership_id: library.membership_id,
                    section_id: contentLibraryCourseSection.id,
                    content_library_id: contentLibrary.id,
                    content_library_course_id: contentLibraryCourse.id,
                    resource_files: assets || [],
                  },
                })

                lessonCount++
              }
            }
          } else {
            // If no libraries exist for the membership, create default data
            await createDefaultLesson(contentLibrary, contentLibraryCourse, contentLibraryCourseSection, membership)

            lessonCount++
            defaultLessonCount++
          }
        }
      } else {
        // If no related categories exist for the membership, create default data
        const contentLibraryCourseSection = await prisma.contentLibraryCourseSection.create({
          data: {
            name: 'New Section',
            content_library_course_id: contentLibraryCourse.id,
            membership_id: membership.id,
            content_library_id: contentLibrary.id,
            visibility: 'draft',
          },
        })
        await createDefaultLesson(contentLibrary, contentLibraryCourse, contentLibraryCourseSection, membership)

        defaultLessonCount++
      }

      processedMemberships++
      membershipModified ? modifiedMemberships++ : unmodifiedMemberships++
      console.log(
        `Processed ${processedMemberships} out of ${memberships.length} memberships. Progress: ${Math.round(
          (processedMemberships / memberships.length) * 100,
        )}% Membership ID: ${membership.id}  Membership Modified: ${membershipModified}`,
      )
    }

    console.log(`Total memberships processed: ${processedMemberships}`)
    console.log(`Memberships modified: ${modifiedMemberships}`)
    console.log(`Memberships unmodified: ${unmodifiedMemberships}`)
    console.log(`Created ${defaultLessonCount} default lessons.`)
    console.log(`Total lesson count: ${lessonCount}`)
  } catch (error) {
    console.error('Error migrating data:', error)
  } finally {
    await prisma.$disconnect()
    console.log('Disconnected from Prisma.')
  }
}

async function createDefaultLesson(contentLibrary, contentLibraryCourse, contentLibraryCourseSection, membership) {
  await prisma.contentLibraryCourseLesson.create({
    data: {
      title: 'New Lesson',
      text: '',
      type: 'text',
      sequence: 0,
      visibility: 'draft',
      membership_id: membership.id,
      section_id: contentLibraryCourseSection.id,
      content_library_id: contentLibrary.id,
      content_library_course_id: contentLibraryCourse.id,
      resource_files: [],
    },
  })
}

migrateData()
  .catch((e) => {
    throw e
  })
  .finally(async () => {
    await prisma.$disconnect()
  })

/* 

doppler run -- node apps/memberup/scripts/migrate-libraries.js

*/
