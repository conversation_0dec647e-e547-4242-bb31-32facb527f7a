const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function calculateRankings(truncate = false) {
  console.log('Starting rankings calculation...')

  if (truncate) {
    console.log('Truncating rankings table...')
    await prisma.ranking.deleteMany()
    console.log('Rankings table truncated.')
  }

  console.log('Fetching reactions...')
  const reactions = await prisma.reaction.findMany({
    where: {
      type: 'like',
    },
    include: {
      message: {
        select: {
          parent_id: true,
          reply_parent_id: true,
          user_id: true,
          user: {
            select: {
              membership_id: true,
            },
          },
        },
      },
    },
  })
  console.log(`Fetched ${reactions.length} reactions.`)

  // if parent_id and reply_parent_id are not null and are equal, this means it's a comment
  const validReactions = reactions.filter(
    (reaction) =>
      reaction.message.parent_id &&
      reaction.message.reply_parent_id &&
      reaction.message.parent_id === reaction.message.reply_parent_id,
  )
  console.log(`Found ${validReactions.length} valid reactions.`)

  // Group reactions by user and message
  console.log('Grouping reactions...')
  const groupedReactions = validReactions.reduce((acc, reaction) => {
    const key = `${reaction.message.user_id}|${reaction.message.user.membership_id}`
    acc[key] = (acc[key] || 0) + 1
    return acc
  }, {})
  console.log(`Grouped reactions into ${Object.keys(groupedReactions).length} groups.`)

  // Prepare data for batch insert
  console.log('Preparing data for batch insert...')
  const rankingData = Object.entries(groupedReactions).map(([key, count]) => {
    const [user_id, membership_id] = key.split('|')
    return {
      user_id,
      membership_id,
      total_points: count,
    }
  })
  console.log(`Prepared data for ${rankingData.length} records.`)

  // Batch insert/update rankings
  console.log('Inserting/updating rankings...')
  let count = 0
  const total = rankingData.length
  const rankings = await prisma.$transaction(
    rankingData.map((data) => {
      count++
      console.log(`Upserting record ${count} of ${total}`)
      return prisma.ranking.upsert({
        where: {
          user_id_membership_id: {
            user_id: data.user_id,
            membership_id: data.membership_id,
          },
        },
        update: {
          total_points: data.total_points,
        },
        create: data,
      })
    }),
  )
  console.log(`Inserted/updated ${rankings.length} rankings.`)

  console.log('Rankings calculation completed.')
}

const args = process.argv.slice(2)
const truncate = args.includes('--truncate')

calculateRankings(truncate).catch((e) => {
  console.error(e)
  process.exit(1)
})

/* 
execute without  Rankings table data deletion: doppler run -- node calculate-rankings.js 

execute with Rankings table data deletion: doppler run -- node calculate-rankings.js --truncate

======
Raw SQL verification query
======

SELECT 
    feeds.user_id, 
    users.membership_id, 
    COUNT(*) as total_points
FROM 
    reactions
JOIN 
    feeds ON reactions.message_id = feeds.id
JOIN 
    users ON feeds.user_id = users.id
WHERE 
    reactions.type = 'like' 
    AND feeds.parent_id IS NOT NULL 
    AND feeds.reply_parent_id IS NOT NULL 
    AND feeds.parent_id = feeds.reply_parent_id
GROUP BY 
    feeds.user_id, 
    users.membership_id;

*/
