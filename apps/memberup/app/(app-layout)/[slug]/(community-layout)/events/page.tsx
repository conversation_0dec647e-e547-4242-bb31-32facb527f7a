'use client'

import AddIcon from '@mui/icons-material/Add'
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns'
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider'
import { endOfDay, endOfMonth, isBefore, isThisMonth, startOfMonth } from 'date-fns'
import _groupBy from 'lodash/groupBy'
import { useRouter } from 'next/navigation'
import { useEffect, useMemo, useState } from 'react'

import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { EVENT_STATUS_ENUM, EVENT_VIEW_TYPE_ENUM, VISIBILITY_ENUM } from '@memberup/shared/src/types/enum'
import { IEvent } from '@memberup/shared/src/types/interfaces'
import { CommunityDetails } from '@/components/community/community-details'
import { ChevronLeft20Icon, ChevronRight20Icon } from '@/components/icons'
import { Button } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { useRouteProtection } from '@/hooks/useRouteProtection'
import { useStore } from '@/hooks/useStore'
import LoadingSpinner from '@/memberup/components/common/loaders/loading-spinner'
import EditEvent from '@/memberup/components/dialogs/events/edit-event'
import EventCard from '@/memberup/components/events/event-card'
import { getCommunityBaseURL } from '@/memberup/libs/utils'
import { getEventsApi } from '@/shared-services/apis/event.api'

export const selectEvents = (events, type: string, nowDate: Date) => {
  const now = Math.floor(nowDate.getTime() / 1000)

  // Map events and add year/month info
  let docs = events.map((item) => {
    const eventDate = new Date(item.start_time * 1000)
    return {
      ...item,
      year: eventDate.getFullYear(),
      month: eventDate.getMonth() + 1,
    }
  })

  if (type === EVENT_VIEW_TYPE_ENUM.past) {
    docs = docs.filter((item) => item.end_time < now)
  } else {
    const currentDate = new Date()
    const isCurrentMonth =
      nowDate.getMonth() === currentDate.getMonth() && nowDate.getFullYear() === currentDate.getFullYear()

    if (isCurrentMonth) {
      docs = docs.filter((item) => item.end_time > now)
    }
  }

  // Group events by month (same structure for both past and upcoming)
  const groupedDocs = _groupBy(docs, (item) => `${item.year}+${item.month < 10 ? '0' : ''}${item.month}`)
  return groupedDocs
}

export default function EventsPage() {
  const [requestEvents, setRequestEvents] = useState(false)
  const router = useRouter()
  const userCanManageCommunity = useStore((state) => state.community.userCanManageCommunity)
  const [selectedDate, setSelectedDate] = useState<Date>(new Date())
  const membership = useStore((state) => state.community.membership)
  const user = useStore((state) => state.auth.user)
  const [eventViewType, setEventViewType] = useState<EVENT_VIEW_TYPE_ENUM>(EVENT_VIEW_TYPE_ENUM.upcoming)
  const [allEvents, setAllEvents] = useState<IEvent[]>([])
  const [selectedEvent, setSelectedEvent] = useState<IEvent | null>(null)
  const [createOrEditEvent, setCreateOrEditEvent] = useState(false)
  const [showPastEvents, setShowPastEvents] = useState(false)
  const isDraftOrPublishedUpcomingViewType = (event: IEvent): EVENT_VIEW_TYPE_ENUM => {
    return event.status === EVENT_STATUS_ENUM.drafts ? EVENT_VIEW_TYPE_ENUM.draft : EVENT_VIEW_TYPE_ENUM.upcoming
  }

  useRouteProtection()

  const shouldResetToUpcoming = (newDate: Date) => {
    const now = new Date()
    return newDate > now || (newDate.getMonth() === now.getMonth() && newDate.getFullYear() === now.getFullYear())
  }
  const handlePreviousMonth = () => {
    setSelectedDate((prev) => {
      const newDate = new Date(prev.getFullYear(), prev.getMonth() - 1, 1)

      if (shouldResetToUpcoming(newDate)) {
        setEventViewType(EVENT_VIEW_TYPE_ENUM.upcoming)
        setShowPastEvents(false)
      }
      return newDate
    })
  }
  const handleNextMonth = () => {
    setSelectedDate((prev) => {
      const newDate = new Date(prev)
      newDate.setMonth(prev.getMonth() + 1)

      if (shouldResetToUpcoming(newDate)) {
        setEventViewType(EVENT_VIEW_TYPE_ENUM.upcoming)
        setShowPastEvents(false)
      }
      return newDate
    })
  }

  const handleToday = () => {
    setSelectedDate(new Date())
    setEventViewType(EVENT_VIEW_TYPE_ENUM.upcoming)
    setShowPastEvents(false)
  }

  async function loadEvents() {
    try {
      setRequestEvents(true)
      let where = {}
      const now = Math.floor(new Date().getTime() / 1000)
      const startOfSelectedMonth = Math.floor(startOfMonth(selectedDate).getTime() / 1000)
      const endOfSelectedMonth = Math.floor(endOfMonth(selectedDate).getTime() / 1000)

      const statusCondition = userCanManageCommunity
        ? { status: { in: [EVENT_STATUS_ENUM.drafts, EVENT_STATUS_ENUM.published] } }
        : { status: EVENT_STATUS_ENUM.published }

      if (isThisMonth(selectedDate)) {
        if (showPastEvents) {
          const endOfDayTimestamp = Math.floor(endOfDay(new Date(now * 1000)).getTime() / 1000)

          where = {
            AND: [
              {
                start_time: {
                  gte: startOfSelectedMonth,
                },
              },
              {
                start_time: {
                  lte: endOfDayTimestamp,
                },
              },
            ],
            ...statusCondition,
          }
        } else {
          where = {
            OR: [
              {
                start_time: {
                  gte: now,
                  lte: endOfSelectedMonth,
                },
              },
              {
                end_time: {
                  gte: now,
                  lte: endOfSelectedMonth,
                },
              },
            ],
            ...statusCondition,
          }
        }
      } else {
        // Past or future month logic
        where = {
          start_time: {
            gte: startOfSelectedMonth,
            lte: endOfSelectedMonth,
          },
          ...statusCondition,
        }
      }

      const res = await getEventsApi(membership.id, {
        where: JSON.stringify(where),
        orderBy: JSON.stringify([{ start_time: 'asc' }, { end_time: 'asc' }]),
      })
      setAllEvents(res.data.data.docs)
    } catch (e) {
      toast.error(e.message)
    } finally {
      setRequestEvents(false)
    }
  }

  const nowEvents = selectEvents(allEvents, 'now', selectedDate) || {}
  const events = selectEvents(allEvents, eventViewType, selectedDate) || {}

  const handleCreateEventOnClose = () => {
    setCreateOrEditEvent(false)
    setSelectedEvent(null)
    loadEvents()
  }

  const handleOnEditEventClick = (event: IEvent) => {
    setSelectedEvent(event)
    setCreateOrEditEvent(true)
  }

  const handleOnDeleteSuccess = () => {
    loadEvents()
  }

  useEffect(() => {
    if (!membership || (membership.membership_setting.visibility === VISIBILITY_ENUM.private && !user)) return

    loadEvents()
  }, [membership, selectedDate, showPastEvents, user])

  // This is being saved for future use when we have a way to show happening now events
  // const renderHappeningNow = useMemo(() => {
  //   const temp = new Date()
  //   temp.setHours(0, 0, 0)
  //   const startTime = temp.getTime()
  //   temp.setHours(23, 59, 59)
  //   const endTime = temp.getTime()
  //   const time = selectedDate.getTime()
  //   if (time >= startTime && time <= endTime) {
  //     return 'Happening Now'
  //   }
  //   return `Happening on ${formatDate({ date: time, format: 'MMM dd' })}`
  // }, [selectedDate])

  const renderEvents = useMemo(() => {
    if (showPastEvents && isThisMonth(selectedDate)) {
      // For current month past events, we need to separate past and today events
      const now = Math.floor(new Date().getTime() / 1000)
      const todayStart = new Date(selectedDate)
      todayStart.setHours(0, 0, 0, 0)
      const todayStartTimestamp = Math.floor(todayStart.getTime() / 1000)

      const docs = allEvents.map((item) => {
        const eventDate = new Date(item.start_time * 1000)
        return {
          ...item,
          year: eventDate.getFullYear(),
          month: eventDate.getMonth() + 1,
          isToday: item.start_time >= todayStartTimestamp && item.start_time < todayStartTimestamp + 86400,
          isPast: item.end_time < now,
        }
      })

      const pastEvents = docs.filter((item) => item.isPast)
      const groupedPastEvents = _groupBy(
        pastEvents,
        (item) => `${item.year}+${item.month < 10 ? '0' : ''}${item.month}`,
      )
      const todayEvents = docs.filter((item) => item.isToday && !item.isPast)

      return (
        <>
          {/* Past events */}
          <div className="flex flex-col gap-[0.1875rem] md:gap-2">
            {Object.values(groupedPastEvents)
              .flat()
              .map((event: IEvent) => (
                <EventCard
                  key={event.id}
                  appEvent={event}
                  editable={true}
                  eventViewType={EVENT_VIEW_TYPE_ENUM.past}
                  onClick={() => router.push(`${getCommunityBaseURL(membership)}/events/${event.id}`)}
                  onEditEventClick={handleOnEditEventClick}
                  onDeleteSuccess={handleOnDeleteSuccess}
                  useLocalTimeZone={true}
                />
              ))}
          </div>

          {/* Today separator */}
          {todayEvents.length > 0 && (
            <div className="flex h-[2.6875rem] w-full items-center justify-center font-semibold text-black-700 dark:bg-black-700 dark:text-black-100 md:h-6">
              Today
            </div>
          )}

          {/* Today's events (ongoing + upcoming) */}
          <div className="flex flex-col gap-[0.1875rem] md:gap-2">
            {todayEvents.map((event: IEvent) => (
              <EventCard
                key={event.id}
                appEvent={event}
                editable={true}
                eventViewType={isDraftOrPublishedUpcomingViewType(event)}
                onClick={() => router.push(`${getCommunityBaseURL(membership)}/events/${event.id}`)}
                onEditEventClick={handleOnEditEventClick}
                onDeleteSuccess={handleOnDeleteSuccess}
                useLocalTimeZone={true}
              />
            ))}
          </div>
        </>
      )
    }

    // Regular view (upcoming events or past events for past months)
    const keys = Object.keys(events).sort()

    return (
      <>
        {keys.map((key) => (
          <div key={key} className="events-month">
            <div className="flex flex-col gap-[0.1875rem] md:gap-2">
              {events[key].map((event: IEvent) => (
                <EventCard
                  key={event.id}
                  appEvent={event}
                  editable={true}
                  eventViewType={
                    showPastEvents || isBefore(selectedDate, new Date())
                      ? EVENT_VIEW_TYPE_ENUM.past
                      : isDraftOrPublishedUpcomingViewType(event)
                  }
                  onClick={() => router.push(`${getCommunityBaseURL(membership)}/events/${event.id}`)}
                  onEditEventClick={handleOnEditEventClick}
                  onDeleteSuccess={handleOnDeleteSuccess}
                  useLocalTimeZone={true}
                />
              ))}
            </div>
          </div>
        ))}
      </>
    )
  }, [events, showPastEvents, selectedDate])

  const showPastEventsButton = isThisMonth(selectedDate) && !showPastEvents

  return (
    <div className="space-y:6 page-inner-pb mt-[-1.1875rem] flex w-full flex-col-reverse items-start justify-between md:mt-0 md:flex-row md:space-x-6 md:space-y-0">
      <div className="flex w-full flex-col gap-[0.1875rem] bg-grey-200 dark:bg-black-500 md:gap-2 md:bg-grey-100 md:dark:bg-black-700">
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          {/* Page header */}
          <div className="flex w-full flex-row justify-between bg-white-100 p-5 dark:bg-black-700 md:bg-grey-100 md:px-0 md:py-[10px]">
            <Button
              className={`hidden h-8 items-center justify-center rounded-base border border-black-200 bg-white-100 p-[0.625rem] py-[0.375rem] text-ssm font-semibold text-black-200 dark:border-none dark:bg-black-500 dark:text-black-100 md:flex md:h-10 md:text-sm ${isThisMonth(selectedDate) ? 'invisible' : 'visible'}`}
              onClick={handleToday}
            >
              Today
            </Button>

            <div className="flex flex-row items-center gap-[0.8125rem] md:gap-12">
              <div className="flex w-full max-w-[60px] flex-row items-center justify-between gap-5 md:hidden">
                <button type="button" onClick={handlePreviousMonth} className="bg-transparent">
                  <ChevronLeft20Icon className="text-black-200 dark:text-black-100" />
                </button>
                <button type="button" onClick={handleNextMonth} className="bg-transparent">
                  <ChevronRight20Icon className="text-black-200 dark:text-black-100" />
                </button>
              </div>

              <button type="button" onClick={handlePreviousMonth} className="hidden bg-transparent md:block">
                <ChevronLeft20Icon className="text-black-200 dark:text-black-100" />
              </button>

              <div className="font-semibold text-black-700 dark:text-white-500">
                {formatDate({ date: selectedDate, format: 'MMM yyyy' })}
              </div>

              <button type="button" onClick={handleNextMonth} className="hidden bg-transparent md:block">
                <ChevronRight20Icon className="text-black-200 dark:text-black-100" />
              </button>
            </div>

            <div className="flex flex-row gap-[0.6512rem] rounded-lg">
              {userCanManageCommunity && (
                <Button
                  className="h-8 w-8 flex-shrink-0 rounded-full bg-primary-200 md:h-10 md:w-10"
                  disabled={requestEvents}
                  shape="circular"
                  onClick={() => setCreateOrEditEvent(true)}
                >
                  <AddIcon />
                </Button>
              )}
            </div>
          </div>

          {showPastEventsButton && (
            <Button
              variant="secondary"
              className="w-full rounded-none bg-white-100 p-4 text-sm font-semibold text-black-200 hover:bg-grey-300 dark:bg-black-700 dark:text-black-100 md:rounded-base md:bg-white-500 md:text-base md:dark:bg-black-500"
              onClick={() => setShowPastEvents(true)}
            >
              Show past events in {formatDate({ date: selectedDate, format: 'MMMM' })}
            </Button>
          )}

          {!showPastEventsButton && isThisMonth(selectedDate) && (
            <Button
              variant="secondary"
              className="w-full rounded-none bg-white-100 p-4 text-sm font-semibold text-black-200 hover:bg-grey-300 dark:bg-black-700 dark:text-black-100 md:rounded-base md:bg-white-500 md:text-base md:dark:bg-black-500"
              onClick={() => setShowPastEvents(false)}
            >
              Hide past events in {formatDate({ date: selectedDate, format: 'MMMM' })}
            </Button>
          )}

          <div className="flex flex-col gap-[0.1875rem] md:gap-2">
            {!requestEvents && Object.keys(nowEvents).length === 0 && Object.keys(events).length === 0 && (
              <div className="bg-white-100 p-4 text-center dark:bg-black-700 md:rounded-base md:bg-white-500">
                {`There are no ${
                  !showPastEvents && (isThisMonth(selectedDate) || isBefore(new Date(), selectedDate)) ? 'upcoming' : ''
                } events for this month.`}
              </div>
            )}

            {requestEvents ? <LoadingSpinner /> : <>{renderEvents}</>}
          </div>
        </LocalizationProvider>

        <EditEvent data={selectedEvent} open={createOrEditEvent} onClose={() => handleCreateEventOnClose()} />
      </div>
      <CommunityDetails />
    </div>
  )
}
