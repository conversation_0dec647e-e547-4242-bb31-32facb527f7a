import { baseApi } from './base.api'

export const getMuxAssetsApi = (params: { upload_id: string }) => {
  return baseApi().get(`/api/mux/assets`, { params })
}

export const getMuxAssetApi = (id: string) => {
  return baseApi().get(`/api/mux/assets/${id}`)
}

export const getMuxUploadApi = (uploadId: string) => {
  return baseApi().get(`/api/mux/upload/${uploadId}`)
}

export const getMuxUploadDataApi = (
  passthrough: string,
  createWithTranscriptions: boolean = false,
  membershipId: string,
  generateRenditions: boolean = false,
) => {
  return baseApi().get(`/api/mux/upload-url`, {
    params: {
      passthrough,
      create_with_transcriptions: createWithTranscriptions,
      membership_id: membershipId,
      generate_renditions: generateRenditions,
    },
  })
}

export const getMuxTranscriptionApi = (fileMetadata: any) => {
  return baseApi().post(`/api/mux/transcription`, { file_metadata: fileMetadata })
}
