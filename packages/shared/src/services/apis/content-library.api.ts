import { baseApi } from './base.api'
import { getSlug } from '@memberup/shared/src/libs/host'

export const getContentLibraryApi = async (membershipId: string) => {
  //const membershipSlug = getSlug(location.hostname)
  const res = await baseApi().get(`/api/content-library?membership_id=${membershipId}`)
  return res
}

export const updateContentLibraryApi = async (libraryId, data) => {
  const res = await baseApi().put(`/api/content-library/${libraryId}`, data)
  return res
}

export const createCourseApi = async (data) => {
  const res = await baseApi().post(`/api/content-library/${data.content_library_id}/course`, data)
  return res
}

// TODO: Check if this is used
export const getUserCoursesApi = async (contentLibraryId, membershipId) => {
  const res = await baseApi().get(`/api/content-library/${contentLibraryId}/course?membership_id=${membershipId}`)
  return res
}

export const getContentLibraryCourseApi = async (contentLibraryId, courseId, membershipId) => {
  const res = await baseApi().get(
    `/api/content-library/${contentLibraryId}/course/${courseId}?membership_id=${membershipId}`,
  )
  return res
}

export const deleteCourseApi = async (contentLibraryId, courseId) => {
  const res = await baseApi().delete(`/api/content-library/${contentLibraryId}/course/${courseId}`)
  return res
}

export const createSectionApi = async (courseId, data, membershipId) => {
  const res = await baseApi().post(
    `/api/content-library/${data.content_library_id}/course/${courseId}/section?membership_id=${membershipId}`,
    data,
  )
  return res
}

export const updateSectionApi = async (contentLibraryId, courseId, sectionId, data) => {
  const res = await baseApi().put(
    `/api/content-library/${contentLibraryId}/course/${courseId}/section/${sectionId}`,
    data,
  )
  return res
}

export const updateSectionsOrderApi = async (newOrders) => {
  const res = await baseApi().put(`/api/content-library/undefined/course/undefined/section/order`, {
    newOrders,
  })
  return res
}

export const deleteSectionApi = async (contentLibraryId, courseId, sectionId) => {
  const res = await baseApi().delete(`/api/content-library/${contentLibraryId}/course/${courseId}/section/${sectionId}`)
  return res
}

export const createLessonApi = async (contentLibraryId, courseId, sectionId, data, membershipId) => {
  const res = await baseApi().post(
    `/api/content-library/${contentLibraryId}/course/${courseId}/section/${sectionId}/lesson?membership_id=${membershipId}`,
    data,
  )
  return res
}

export const updateLessonsOrderApi = async (newOrders) => {
  const res = await baseApi().put(`/api/content-library/undefined/course/undefined/section/undefined/lesson/order`, {
    newOrders,
  })
  return res
}

export const updateLessonApi = async (contentLibraryId, courseId, sectionId, lessonId, data) => {
  const res = await baseApi().put(
    `/api/content-library/${contentLibraryId}/course/${courseId}/section/${sectionId}/lesson/${lessonId}`,
    data,
  )
  return res
}

export const deleteLessonApi = async (contentLibraryId, courseId, sectionId, lessonId) => {
  const res = await baseApi().delete(
    `/api/content-library/${contentLibraryId}/course/${courseId}/section/${sectionId}/lesson/${lessonId}`,
  )
  return res
}

export const updateLibraryCourseApi = (libraryId: string, courseId: string, payload: any) => {
  return baseApi().put(`/api/content-library/${libraryId}/course/${courseId}`, payload)
}

export const getUserLessonStatusApi = (libraryId: string, courseId: string, sectionId: string, lessonId: string) => {
  return baseApi().get(
    `/api/content-library/${libraryId}/course/${courseId}/section/${sectionId}/lesson/${lessonId}/user-statuses`,
  )
}

export const updateUserLessonStatusApi = (libraryId: string, courseId: string, lessonId: string, payload: any) => {
  return baseApi().put(
    `/api/content-library/${libraryId}/course/${courseId}/section/undefined/lesson/${lessonId}/user-statuses`,
    payload,
  )
}

export const restartCourseApi = (libraryId: string, courseId: string) => {
  return baseApi().delete(`/api/content-library/${libraryId}/course/${courseId}/restart-course`)
}
