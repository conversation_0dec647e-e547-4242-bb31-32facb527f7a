import { baseApi } from './base.api'

export const getAnalyticsMembersApi = (
  body: {
    gte: number
    lte: number
    isNew?: boolean
    isCancel?: boolean
    isTotal?: boolean
  },
  membershipId: string,
) => {
  return baseApi().post(`/api/analytics/members?membership_id=${membershipId}`, body)
}

export const getAnalyticsReveneuApi = (body: { gte: number; lte: number }, membershipId) => {
  return baseApi().post(`/api/analytics/revenue?membership_id=${membershipId}`, body)
}
