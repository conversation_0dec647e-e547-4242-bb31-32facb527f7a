import axios, { CancelTokenSource } from 'axios'

import { baseApi } from './base.api'
import { CLOUD_NAME, CLOUD_UPLOAD_PRESET } from '@/shared-config/envs'
import { sleep } from '@/shared-libs/common'

let arr: any
arr = [{ format: 'auto', quality: 'auto' }]

const slice = (file, start, end) => {
  var slice = file.mozSlice ? file.mozSlice : file.webkitSlice ? file.webkitSlice : file.slice ? file.slice : noop

  return slice.bind(file)(start, end)
}

const noop = () => {}

export const deleteFileFromCloudinaryApi = async (publicId: string, resource_type: string) => {
  try {
    await baseApi().delete(`/api/cloudinary/${publicId}?resource_type=${resource_type}`)

    return { success: true, message: 'File deleted successfully' }
  } catch (error) {
    console.error(error.message)
    return { success: false, message: error.message }
  }
}

let cancelTokenSource: CancelTokenSource | null = null

export const uploadFileToCloudinaryApi = async (
  file: File,
  resource_type?: string,
  onUploadProgress?: (progress: number) => void,
  onCancelTokenCreated?: any,
) => {
  const url = `https://api.cloudinary.com/v1_1/${CLOUD_NAME}/${
    ['image', 'raw', 'video'].includes(resource_type) ? resource_type : 'auto'
  }/upload`
  const XUniqueUploadId = +new Date()
  const size = file.size
  const sliceSize = 20000000
  const progressPerSlice = sliceSize / Math.max(sliceSize, size)
  const progress = []
  const promises = []
  cancelTokenSource = axios.CancelToken.source()
  if (onCancelTokenCreated) {
    onCancelTokenCreated(cancelTokenSource)
  }
  let start = 0
  let end = 0
  let i = 0

  const send = (index, piece, start, end, size) => {
    const formdata = new FormData()

    formdata.append('file', piece)
    formdata.append('cloud_name', CLOUD_NAME)
    formdata.append('upload_preset', CLOUD_UPLOAD_PRESET)
    formdata.append('filename_override', file.name)
    // formdata.append("public_id", "chunkedFile1")
    // TODO: append eager params to formData if you want to optimize upload before rendering on screen
    // formdata.append('eager', arr)

    return axios.post(url, formdata, {
      headers: {
        'X-Unique-Upload-Id': XUniqueUploadId,
        'Content-Range': 'bytes ' + start + '-' + end + '/' + size,
      },
      cancelToken: cancelTokenSource.token,
      ...(onUploadProgress
        ? {
            onUploadProgress: (progressEvent) => {
              progress[index] = (progressEvent.loaded / progressEvent.total) * progressPerSlice
              onUploadProgress(progress.reduce((prevValue, currentValue) => prevValue + currentValue, 0))
            },
          }
        : {}),
    })
  }

  do {
    end = start + sliceSize
    if (end > size) {
      end = size
    }
    sleep(3)

    const s = slice(file, start, end)
    promises.push(send(i, s, start, end - 1, size))
    i++
    start += sliceSize
  } while (end < size)

  return Promise.all(promises)
    .then((values) => {
      return values.find((item) => Boolean(item.data?.secure_url)) || null
    })
    .catch((err) => {
      if (!['ERR_CANCELED', 'ERR_NETWORK', 'ECONNABORTED'].includes(err?.code)) {
        console.error(err)
      }
      return null
    })
}

export const cancelUpload = () => {
  if (cancelTokenSource) {
    cancelTokenSource.cancel('Upload cancelled by user')
  }
}
