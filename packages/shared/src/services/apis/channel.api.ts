import { baseApi } from './base.api'
import { IChannel } from '@/shared-types/interfaces'
import { TGetApiParams } from '@/shared-types/types'

export const createChannelApi = (payload: Partial<IChannel>, membershipId: string) => {
  return baseApi().post(`/api/channel?membership_id=${membershipId}`, payload)
}

export const updateChannelApi = (id: string, data: Partial<IChannel>) => {
  return baseApi().put(`/api/channel/${id}`, data)
}

export const getChannelsApi = ({ where, take, skip, membershipId }: TGetApiParams) => {
  return baseApi().get(`/api/channel`, { params: { where, take, skip, membershipId } })
}

export const getChannelApi = (id: string) => {
  return baseApi().get(`/api/channel/${id}`)
}

export const deleteChannelApi = (id: string) => {
  return baseApi().delete(`/api/channel/${id}`)
}

export const bulkUpdateChannelSequencesApi = (
  {
    updates,
  }: {
    updates: { where: { id: string }; data: { sequence: number } }[]
  },
  membershipId: string,
) => {
  return baseApi().post(`/api/channel/bulk-update-sequences?membership_id=${membershipId}`, {
    updates,
  })
}
