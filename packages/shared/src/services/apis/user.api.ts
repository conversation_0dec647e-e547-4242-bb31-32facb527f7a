import { baseApi } from './base.api'
import { IUser } from '@/shared-types/interfaces'
import { TGetApiParams, TStartMembershipPayload } from '@/shared-types/types'

export const startMembershipApi = (data: TStartMembershipPayload) => {
  return baseApi().post(`/api/user/membership`, data)
}

export const exportMembersApi = () => {
  return baseApi().get(`/api/user/export`)
}

export const getMemberCoursesProgressApi = (username: string, membershipId: string) => {
  return baseApi().get(`/api/user/user-profile/${username}/courses-progress?membership_id=${membershipId}`)
}

export const getMemberRequestResponsesApi = (username: string, membershipId: string) => {
  return baseApi().get(`/api/user/user-profile/${username}/member-request-responses?membership_id=${membershipId}`)
}

export const getUsersApi = ({ membershipId, where, take, skip, orderBy }: TGetApiParams, getProgress?: boolean) => {
  return baseApi().get(`/api/user`, {
    params: { where, take, skip, orderBy, getProgress, membership_id: membershipId },
  })
}

export const getActiveUserApi = (params?: { time_zone?: string }) => {
  return baseApi().get(`/api/user/active-user`, { params })
}

export const getActiveUserProfileApi = () => {
  return baseApi().get(`/api/user/active-user-profile`)
}

export const getUserApi = (id: string) => {
  return baseApi().get(`/api/user/${id}`)
}

export const getUserProfile = (username: string) => {
  return baseApi().get(`/api/user/user-profile/${username}`)
}

export const getUserActivityCalendar = (username: string) => {
  return baseApi().get(`/api/user/user-profile/${username}/activity-calendar`)
}

export const updateUserApi = (id: string, data: Partial<IUser> & { new_password?: string }) => {
  return baseApi().put(`/api/user/${id}`, data)
}

export const updateUserProfileApi = (id: string, data: Partial<IUser>) => {
  return baseApi().put(`/api/user/profile`, data, { params: { id } })
}

export const hidePinnedPostApi = (params: { hide: boolean; post_id: string }) => {
  return baseApi().post(`/api/user/profile/hide-pinned-post`, params)
}
export const hideSparkApi = (params: { hide: boolean }) => {
  return baseApi().post(`/api/user/profile/hide-spark`, params)
}
export const checkExistingUserApi = (params: { email?: string; user_name?: string }) => {
  return baseApi().get(`/api/user/check-existing`, params)
}

export const resendEmailVerificationCodeApi = () => {
  return baseApi().post(`/api/user/email-verification/resend-code`, {})
}

export const verifyEmailApi = (code: string) => {
  return baseApi().post(`/api/user/email-verification/verify`, { code })
}

export const checkUserPasswordApi = (data: { password: string }) => {
  return baseApi().post(`/api/user/check-password`, data)
}

export const banUserApi = (id, reason) => {
  return baseApi().post(`/api/user/ban-user`, { id, reason })
}

export const deleteUserApi = (membershipId: string, id: string, ban?: boolean) => {
  return baseApi().delete(`/api/user/${id}?ban=${ban}&membership_id=${membershipId}`)
}

export const sendCancelMembershipEmailApi = (membershipId: string) => {
  return baseApi().post(`/api/user/cancel-membership-email?membership_id=${membershipId}`, {})
}

export const cancelMembershipApi = (membershipId: string) => {
  return baseApi().post(`/api/user/cancel-membership?membership_id=${membershipId}`, {})
}

export const addFeedbackApi = (data: string[]) => {
  return baseApi().post(`/api/user/feedback`, { feedback: data })
}
