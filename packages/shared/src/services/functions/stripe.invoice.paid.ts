import { inngest } from '../inngest'
import { USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { setupMembership } from '@/shared-libs/membership'
import { findMembership } from '@/shared-libs/prisma/membership'
import { updateUser } from '@/shared-libs/prisma/user'
import { findUserProfile, updateUserProfile } from '@/shared-libs/prisma/user-profile'
import { setupUser } from '@/shared-libs/user'
import { IUser, IUserProfile } from '@/shared-types/interfaces'

export const setupAndUpdateUser = async (stripeData, profileUpdateData) => {
  const userProfile = await findUserProfile({
    where: {
      stripe_customer_id: stripeData['customer'],
    },
    include: {
      user: {
        include: {
          membership: {
            include: {
              membership_setting: true,
            },
          },
        },
      },
    },
  })
  if (!userProfile) {
    return
  }

  const { user, ...profile } = userProfile as IUserProfile & { user: IUser }
  if (user && user.status !== USER_STATUS_ENUM.active) {
    const { membership, ...userData } = user
    let setupUserSuccess = false
    if (membership) {
      const { membership_setting, ...membershipData } = membership
      setupUserSuccess = await setupUser(
        {
          ...userData,
          status: USER_STATUS_ENUM.active,
          profile: { ...profile, active: true },
        },
        membershipData,
        membership_setting,
        user.status === USER_STATUS_ENUM.inactive,
        true,
        true,
        user.status === USER_STATUS_ENUM.inactive,
      )
    }
    if (setupUserSuccess) {
      await updateUser({
        where: {
          id: user.id,
        },
        data: {
          status: USER_STATUS_ENUM.active,
          profile: {
            update: profileUpdateData,
          },
        },
      })
    }
  } else {
    await updateUserProfile({
      where: {
        id: userProfile.id,
      },
      data: profileUpdateData,
    })
  }
}

export const setupAndUpdateMembership = async (stripeData) => {
  const membershipId = stripeData['parent']['subscription_details']['metadata']['membership_id']
  const membership = (await findMembership({
    where: {
      id: membershipId,
    },
    include: {
      membership_setting: true,
      owner: true,
    },
  })) as any
  if (!membership) {
    console.error(`Cannot find membership with id: ${membershipId}`)
    return
  }
  if (membership && !membership.active) {
    await setupMembership(membership)
  }
}

export default inngest.createFunction(
  { id: 'invoice-paid', name: 'Invoice Paid' },
  { event: 'stripe/invoice.paid' },
  async ({ event, step }) => {
    const { data } = event
    await step.run('invoice-paid', async () => {
      if (data['isMembershipStripe']) {
        await setupAndUpdateUser(data, { active: true })
      } else {
        // Memberup
        // const membershipSettingUpdateData = {
        //   stripe_subscription_discount_id:
        //     data['discount']?.coupon?.id === STRIPE_INTERNAL_DISCOUNT_ID ? null : data['discount']?.id || null,
        // }
        await setupAndUpdateMembership(data)
      }
    })
  },
)
