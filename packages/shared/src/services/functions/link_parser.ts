import axios from 'axios'
import cheerio from 'cheerio'
import parse from 'html-react-parser'

import { getUrlData } from '@memberup/shared/src/libs/urls'

const getLinksWithPreview = (htmlString) => {
  const urls = []
  parse(htmlString, {
    replace: (domNode: any) => {
      if (domNode.name === 'a' && domNode.attribs && domNode.attribs['data-preview'] === 'true') {
        urls.push(domNode.attribs.href)
      }
    },
  })
  return urls
}

const processLinks = async (text) => {
  const urls = getLinksWithPreview(text)
  console.log(urls)

  const metadataArray = await Promise.allSettled(urls.map(getUrlData))

  const metadata = {}

  metadataArray.forEach((result, index) => {
    if (result.status === 'fulfilled') {
      const linkMetadata = result.value
      metadata[linkMetadata.url] = {
        thumbnail: linkMetadata.imageSrc,
        title: linkMetadata.title,
        description: linkMetadata.description,
      }
    } else {
      // If the promise was rejected, insert null values
      metadata[urls[index]] = {
        thumbnail: null,
        title: null,
        description: null,
      }
    }
  })

  return { links: metadata }
}

const fetchMetadata = async (url) => {
  try {
    const response = await axios.get(url)
    const html = response.data
    const $ = cheerio.load(html)

    const title = $('meta[property="og:title"]').attr('content') || $('title').text()
    const description =
      $('meta[property="og:description"]').attr('content') || $('meta[name="description"]').attr('content') || ''
    let thumbnail = $('meta[property="og:image"]').attr('content') || ''

    /* if (!thumbnail) {
      // Find an img element whose src attribute does not start with "data:" to avoid base64 fallback images
      $('img').each((i, elem) => {
        const imgSrc = $(elem).attr('src');
        if (imgSrc && !imgSrc.startsWith('data:')) {
          // If the image URL is relative, resolve it to an absolute URL
          const baseUrl = urlModule.resolve(url, '/');
          thumbnail = urlModule.resolve(baseUrl, imgSrc);
          return false; 
        }
      });
    } */

    // Include the URL in the returned object
    return {
      url,
      thumbnail,
      title,
      description,
    }
  } catch (error) {
    console.error(`An error occurred while fetching metadata for ${url}:`, error)
    // Include the URL in the returned object
    return {
      url,
      thumbnail: '',
      title: '',
      description: '',
    }
  }
}

export default processLinks
