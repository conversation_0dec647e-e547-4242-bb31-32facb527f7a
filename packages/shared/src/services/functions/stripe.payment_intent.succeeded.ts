import { PAYMENT_TYPE_ENUM, Prisma } from '@prisma/client'
import * as Sentry from '@sentry/nextjs'
import Stripe from 'stripe'

import { inngest } from '../inngest'
import { KNOCK_OBJECT_IDS, knockBulkAddSubscriptions } from '@/shared-libs/knock'
import { createPayment, formatPaymentDescription, mapStripeStatusToPaymentStatus } from '@/shared-libs/prisma/payments'
import prisma from '@/shared-libs/prisma/prisma'
import { stripeGetInvoiceWithSubscription, stripeRetrieveCharge } from '@/shared-libs/stripe'
import { USER_MEMBERSHIP_STATUS_ENUM, USER_ROLE_ENUM } from '@/shared-types/enum'
import { TStripeConnectAccount } from '@/shared-types/types'

// Persist payments into payment history
export default inngest.createFunction(
  { id: 'payment-intent-succeeded', name: 'Payment Intent Succeeded' },
  { event: 'stripe/payment_intent.succeeded' },
  async ({ event, step }) => {
    const { data } = event
    await step.run('payment-intent-succeeded', async () => {
      const stripeCustomerId = data['customer']

      // Membership event handling
      if (data['isMembershipStripe']) {
        // Membership payment
        try {
          // Find the user membership record to get the user_id
          const userMembership = await prisma.userMembership.findFirst({
            where: {
              stripe_customer_id: stripeCustomerId,
            },
            include: {
              membership: {
                include: {
                  membership_setting: true,
                },
              },
            },
          })

          if (!userMembership) {
            throw new Error(`No user membership found for stripe_customer_id: ${stripeCustomerId}`)
          }

          // Accept the user in the community, for one-time payments there are no subscriptions
          const updatedUserMembership = await prisma.userMembership.update({
            where: {
              id: userMembership.id,
            },
            data: {
              status: USER_MEMBERSHIP_STATUS_ENUM.accepted,
              stripe_subscription_id: null,
              stripe_subscription_canceled_at: null,
              stripe_subscription_status: null,
              stripe_subscription_period_start_at: null,
              stripe_subscription_period_end_at: null,
              stripe_subscription_cancel_at_period_end: null,
            },
            include: {
              membership: true,
            },
          })

          await knockBulkAddSubscriptions(userMembership.membership.slug, KNOCK_OBJECT_IDS, [
            updatedUserMembership.user_id,
          ])

          const chargeId = data.latest_charge
          const charge = await stripeRetrieveCharge(
            userMembership.membership.membership_setting?.stripe_connect_account as TStripeConnectAccount,
            chargeId,
          )
          const receiptUrl = charge.receipt_url

          // Create payment history record
          await createPayment({
            data: {
              amount: data.amount / 100, // Convert from cents to dollars
              currency: data.currency || 'usd',
              description: formatPaymentDescription(
                PAYMENT_TYPE_ENUM.membership,
                updatedUserMembership.membership?.name,
              ),
              payment_date: new Date(),
              status: mapStripeStatusToPaymentStatus(data.status),
              payment_type: PAYMENT_TYPE_ENUM.membership,
              stripe_payment_intent_id: data.id,
              stripe_charge_id: chargeId,
              stripe_receipt_url: receiptUrl,
              stripe_customer_id: stripeCustomerId,
              stripe_invoice: null,
              user_id: updatedUserMembership.user_id,
              membership_id: userMembership.membership.id,
            },
          })
        } catch (err) {
          console.error('Error processing payment intent:', err)
          Sentry.captureException(err)
        }
      } else {
        // Memberup payment handling
        try {
          const user = await prisma.user.findFirst({
            where: {
              profile: {
                stripe_customer_id: stripeCustomerId,
              },
            },
          })

          if (!user) {
            throw new Error(`No user found for stripe_customer_id: ${stripeCustomerId}`)
          }

          if (!data.invoice) {
            throw new Error('No invoice ID found in payment intent data')
          }

          const invoice = await stripeGetInvoiceWithSubscription(null, data.invoice)

          const subscription = invoice?.subscription
          const metadata = (subscription as Stripe.Subscription)?.metadata
          const membershipId = metadata?.membership_id

          if (!membershipId) {
            throw new Error('No membership_id found in subscription metadata')
          }

          const userMembership = await prisma.userMembership.findFirst({
            where: {
              user_id: user.id,
              user_role: USER_ROLE_ENUM.owner,
              membership_id: membershipId,
            },
            include: {
              membership: true,
            },
          })

          const chargeId = data.latest_charge
          const charge = await stripeRetrieveCharge(null, chargeId)
          const receiptUrl = charge.receipt_url

          await createPayment({
            data: {
              amount: data.amount / 100, // Convert from cents to dollars
              currency: data.currency || 'usd',
              description: formatPaymentDescription(PAYMENT_TYPE_ENUM.memberup),
              payment_date: new Date(),
              status: mapStripeStatusToPaymentStatus(data.status),
              payment_type: PAYMENT_TYPE_ENUM.memberup,
              stripe_payment_intent_id: data.id,
              stripe_charge_id: chargeId,
              stripe_receipt_url: receiptUrl,
              stripe_customer_id: stripeCustomerId,
              stripe_invoice: invoice,
              user_id: user.id,
              membership_id: userMembership?.membership_id,
            },
          })
        } catch (err) {
          console.error('Error processing payment intent:', err)
          Sentry.captureException(err)
        }
      }
    })
  },
)
