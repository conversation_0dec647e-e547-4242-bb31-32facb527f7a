import _uniq from 'lodash/uniq'
import { updateActiveCampaignContact } from 'memberup/pages/api/membership/complete'

import { inngest } from '../inngest'
import { CHANNEL_TYPE_ENUM, SPACE_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { updateAlgoliaMembersIndexForUserId } from '@/shared-libs/algolia'
import { KNOCK_OBJECT_IDS, KNOCK_OBJECTS, knockAddObjects, knockBulkAddSubscriptions } from '@/shared-libs/knock'
import { createChannel, findChannels } from '@/shared-libs/prisma/channel'
import { findMembership, updateMembership } from '@/shared-libs/prisma/membership'
import { updateMembershipSettings } from '@/shared-libs/prisma/membership-settings'
import prisma from '@/shared-libs/prisma/prisma'
import { updateUser } from '@/shared-libs/prisma/user'
import { findUserMembership } from '@/shared-libs/prisma/user-membership'
import { findUserProfile } from '@/shared-libs/prisma/user-profile'
import { streamChatUpsertUser } from '@/shared-libs/stream-chat'
import { USER_MEMBERSHIP_STATUS_ENUM, USER_ROLE_ENUM, USER_STATUS_ENUM } from '@/shared-types/enum'

const MU_ID = process.env.NEXT_PUBLIC_MU_ID

export default inngest.createFunction(
  { id: 'customer-subscription-updated', name: 'Customer Subscription Updated' },
  { event: 'stripe/customer.subscription.updated' },
  async ({ event, step }) => {
    const { data } = event

    await step.run('update-subscription-status', async () => {
      console.log('data', data)

      if (data['isMembershipStripe']) {
        // TODO: refactor this to use the userMembership tables
        if (data['status'] === 'canceled') {
          const dbUserProfile = await findUserProfile({
            where: { stripe_customer_id: data['customer'] },
          })

          const user = await updateUser({
            where: { id: dbUserProfile.user_id },
            data: {
              status: USER_STATUS_ENUM.deleted,
              profile: {
                update: { active: false },
              },
            },
            include: { profile: true },
          })

          const userProfile = user.profile
          const isNotMember = [USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner].includes(
            user.role || (USER_ROLE_ENUM.member as any),
          )

          await streamChatUpsertUser(
            {
              id: user.id,
              first_name: user.first_name || '',
              last_name: user.last_name || '',
              image: userProfile?.image || user.image,
              image_crop_area: userProfile?.image_crop_area || null,
              role: user.role,
              status: user.status,
            },
            isNotMember ? _uniq([MU_ID, user.membership_id]) : [user.membership_id],
          )
          await updateAlgoliaMembersIndexForUserId(user.id)
        }

        const stripeCustomerId = data['customer']
        const membershipId = data['metadata']['membership_id']

        const userMembership = await findUserMembership({
          where: {
            stripe_customer_id: stripeCustomerId,
            membership_id: membershipId,
          },
          include: {
            membership: true,
          },
        })

        await prisma.userMembership.updateMany({
          where: {
            stripe_customer_id: stripeCustomerId,
            membership_id: membershipId,
          },
          data: {
            status: USER_MEMBERSHIP_STATUS_ENUM.accepted,
            stripe_subscription_id: data['id'],
            stripe_subscription_canceled_at: data['canceled_at'],
            stripe_subscription_status: data['status'],
            stripe_subscription_period_start_at: data['current_period_start'],
            stripe_subscription_period_end_at: data['current_period_end'],
            stripe_subscription_cancel_at_period_end: data['cancel_at_period_end'],
          },
        })

        await knockBulkAddSubscriptions(userMembership.membership.slug, KNOCK_OBJECT_IDS, [userMembership.user_id])
      } else {
        // Subscription for MemberUp
        const membershipId = data['metadata']['membership_id']
        const membershipSetting = await prisma.membershipSetting.findFirst({
          where: {
            membership_id: membershipId,
          },
          include: {
            membership: {
              include: {
                owner: true,
              },
            },
          },
        })
        if (!membershipSetting) {
          console.warn(`Membership setting does not exist for the provided membership id of ${membershipId}`)
          return
        }

        // Persist subscription data
        await updateMembershipSettings({
          where: {
            id: membershipSetting.id,
          },
          data: {
            stripe_subscription_id: data['id'],
            stripe_subscription_canceled_at: data['canceled_at'],
            stripe_subscription_status: data['status'],
            stripe_enable_annual: data['plan']?.interval === 'year',
            stripe_subscription_period_start_at: data['current_period_start'],
            stripe_subscription_period_end_at: data['current_period_end'],
            completed_membership: 1,
          },
        })

        // Activate the community
        if (data.status === 'active') {
          await updateMembership({
            where: {
              id: membershipId,
            },
            data: {
              active: true,
            },
          })
        }

        // Complete the setup of the community, creating channels
        const membership = membershipSetting.membership
        const channelsData = [
          {
            name: 'Announcements',
            title: 'Announcements',
            description: 'Share announcements!',
            slug: 'announcements',
            is_private: true,
          },
          {
            name: 'Introductions',
            title: 'Introductions',
            description: 'Say hello!',
            slug: 'introductions',
            is_private: false,
          },
          {
            name: 'Member Wins',
            title: 'Member Wins',
            description: 'Celebrate your wins!',
            slug: 'member-wins',
            is_private: false,
          },
        ]

        const channels = await findChannels({
          where: {
            membership_id: membershipId,
          },
        })
        if (!channels.docs.length) {
          for (const channelData of channelsData) {
            await createChannel({
              data: {
                ...channelData,
                type: CHANNEL_TYPE_ENUM.livestream,
                space_type: SPACE_TYPE_ENUM.space,
                created_by: {
                  connect: {
                    id: membership.owner_id,
                  },
                },
                membership: {
                  connect: {
                    id: membershipId,
                  },
                },
              },
            }).catch((err) => {
              console.log('err =====', err)
            })
          }
        }
        updateActiveCampaignContact(membership.owner.email, membershipSetting.plan)

        await knockAddObjects(membership.slug, KNOCK_OBJECTS)
        await knockBulkAddSubscriptions(membership.slug, KNOCK_OBJECT_IDS, [membership.owner_id])
      }
    })
  },
)
