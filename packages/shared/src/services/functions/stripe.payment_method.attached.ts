import { inngest } from '../inngest'
import { updateMembershipSettings } from '@/shared-libs/prisma/membership-settings'
import { updateUser } from '@/shared-libs/prisma/user'
import { findUserProfile, updateUserProfile } from '@/shared-libs/prisma/user-profile'
import { setupUser } from '@/shared-libs/user'
import { USER_STATUS_ENUM } from '@/shared-types/enum'
import { IUser, IUserProfile } from '@/shared-types/interfaces'

export default inngest.createFunction(
  { id: 'payment-method-attached', name: 'Payment Method Attached' },
  { event: 'stripe/payment_method.attached' },
  async ({ event, step }) => {
    const { data } = event

    await step.run('update-payment-method-attached', async () => {
      if (data['isMembershipStripe']) {
        const userProfile = await findUserProfile({
          where: {
            stripe_customer_id: data['customer'],
          },
          include: {
            user: {
              include: {
                membership: {
                  include: {
                    membership_setting: true,
                  },
                },
              },
            },
          },
        })
        if (userProfile?.id) {
          const { user, ...profileData } = userProfile as IUserProfile & { user: IUser }
          if (user?.status === USER_STATUS_ENUM.deleted) {
            const { membership, ...userData } = user
            let setupUserSuccess = false
            if (membership) {
              const { membership_setting, ...membershipData } = membership
              setupUserSuccess = await setupUser(
                {
                  ...userData,
                  status: USER_STATUS_ENUM.active,
                  profile: { ...profileData, active: true },
                },
                membershipData,
                membership_setting,
                false,
                true,
                true,
                false,
              )
            }
            if (setupUserSuccess) {
              await updateUser({
                where: {
                  id: user.id,
                },
                data: {
                  status: USER_STATUS_ENUM.active,
                  profile: {
                    update: {
                      active: true,
                      stripe_payment_method_id: data['id'],
                    },
                  },
                },
              })
            }
          } else {
            await updateUserProfile({
              where: {
                id: userProfile.id,
              },
              data: { active: true, stripe_payment_method_id: data['id'] },
            })
          }
        }
      } else {
        await updateMembershipSettings({
          where: {
            stripe_customer_id: data['customer'],
          },
          data: {
            stripe_payment_method_id: data['id'],
          },
        })
      }
    })
  },
)
