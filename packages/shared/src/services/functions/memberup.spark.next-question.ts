import { knockSpark } from 'memberup/src/libs/knock'
import moment from 'moment-timezone'

import { inngest } from '../inngest'
import { SPARK_MEMBERSHIP_QUESTION } from '@memberup/shared/src/types/enum'
import { updateMembershipSetting } from '@/shared-libs/prisma/membership-settings'
import prisma from '@/shared-libs/prisma/prisma'
import { updateSparkMembershipCategory } from '@/shared-libs/prisma/spark-m-category'
import {
  findMembershipsWithSparkEnabled,
  findSparkMembershipQuestions,
  markSparkMembershipQuestionInstanceAsCompleted,
  setCurrentSparkMembershipQuestionInstance,
} from '@/shared-libs/prisma/spark-question'
import { createSparkResponse, getSparkCurrentStreak } from '@/shared-libs/prisma/spark-response'
import { ISparkMembershipCategory } from '@/shared-types/interfaces'

const BATCH_SIZE = 10
const QUESTION_ROTATION_HOUR = '00'

export const runSparkQuestionRotate = inngest.createFunction(
  { id: 'run-spark-question-rotate', name: 'Run Spark Question Rotate' },
  { cron: '15 * * * *' },
  async ({ step }) => {
    await step.sendEvent('send-spark.next-question-event', {
      name: 'memberup/spark.next-question',
      data: {},
    })
  },
)

export async function createNextSparkMembershipQuestionInstance(
  currentMembershipCategoryId: string,
  currentMembershipQuestionId,
  currentMembershipQuestionInstanceId: string,
  questionSequences: string[],
  ownerId: string,
  membershipId: string,
) {
  const sparkMembershipQuestions = await findSparkMembershipQuestions(membershipId, currentMembershipCategoryId)
  if (!sparkMembershipQuestions || sparkMembershipQuestions.length === 0) {
    console.warn('No questions found. Skipping question rotation.')
    return null
  }

  // Recreate the question sequences if needed
  if (!questionSequences || questionSequences.length !== sparkMembershipQuestions.length) {
    questionSequences = sparkMembershipQuestions.map((q) => q.id)
    await prisma.sparkMembershipCategory.update({
      where: {
        id: currentMembershipCategoryId,
      },
      data: {
        question_sequences: questionSequences,
      },
    })
  }

  let nextQuestionIndex = 0
  if (currentMembershipQuestionId) {
    let currentQuestionIndex = questionSequences.indexOf(currentMembershipQuestionId)
    if (currentQuestionIndex !== -1) {
      if (currentQuestionIndex + 1 < questionSequences.length) {
        nextQuestionIndex = currentQuestionIndex + 1
      }
    }
  }
  const nextMembershipQuestionId = questionSequences[nextQuestionIndex]

  // Select the next active question
  const nextMembershipQuestion = sparkMembershipQuestions.find((q) => q.id === nextMembershipQuestionId)

  // Create the current membership question
  const newMembershipQuestionInstance = await prisma.sparkMembershipQuestionInstance.create({
    data: {
      content: nextMembershipQuestion.content,
      spark_membership_question_id: nextMembershipQuestion.id,
      membership_id: membershipId,
      state: 'started',
    },
    select: {
      id: true,
    },
  })

  // NOTE: This looks like it is deprecated for UA
  // if (selectedQuestion.answer) {
  //   let streakCount = await getSparkCurrentStreak(ownerId, membershipId)
  //   streakCount += 1
  //   await createSparkResponse(
  //     {
  //       data: {
  //         content: selectedQuestion.answer,
  //         membership_id: membershipId,
  //         question: {
  //           connect: {
  //             id: selectedQuestion.id,
  //           },
  //         },
  //         m_question: {
  //           connect: {
  //             id: newMembershipQuestion.id,
  //           },
  //         },
  //         user: {
  //           connect: {
  //             id: ownerId,
  //           },
  //         },
  //         streak_count: streakCount,
  //       },
  //     },
  //     currentMembershipCategoryId
  //   )
  // }

  // Mark old question as completed
  if (currentMembershipQuestionInstanceId) {
    await markSparkMembershipQuestionInstanceAsCompleted(currentMembershipQuestionInstanceId)
  }

  await updateMembershipSetting({
    where: { membership_id: membershipId },
    data: {
      spark_current_membership_question_id: nextMembershipQuestion.id,
      spark_current_membership_question_instance_id: newMembershipQuestionInstance.id,
    },
  })

  // Notify users about new spark question
  await knockSpark({
    id: nextMembershipQuestion.id,
    question: nextMembershipQuestion.content,
    membership_id: membershipId,
    user_id: ownerId,
  })

  return newMembershipQuestionInstance
}

export default inngest.createFunction(
  { id: 'spark-question-rotate', name: 'Spark Question Rotate' },
  { event: 'memberup/spark.next-question' },
  async ({ event, step }) => {
    const memberships = await step.run('select-spark-enabled-memberships', async () => {
      const isTimeZoneAtHour = (timeZone, hour) => {
        const timeInZone = moment().tz(timeZone)
        return timeInZone.format('HH') === `${hour}`
      }
      const membershipsWithSparkEnabled = await findMembershipsWithSparkEnabled()
      const filtered = membershipsWithSparkEnabled.filter((m) => {
        // Allow to work on the specified memberships
        if (event.data['memberships'] && !event.data['memberships'].includes(m.membership_id)) {
          return false
        }

        // do not consider time zones for question rotation, just do it immediately
        if (event.data['skipTimeCheck']) {
          return true
        }
        return isTimeZoneAtHour(m.time_zone, QUESTION_ROTATION_HOUR)
      })
      console.log(filtered)
      return filtered
    })

    // Process in batches
    const chunkArray = (array, size) => {
      const chunkedArr = []
      for (let i = 0; i < array.length; i += size) {
        chunkedArr.push(array.slice(i, i + size))
      }
      return chunkedArr
    }

    const membershipBatches = chunkArray(memberships, BATCH_SIZE)

    // Parallelize batches
    const results = await Promise.all(
      membershipBatches.map((membershipBatch) =>
        step.run('process-next-question-batch', async () => {
          const output = []
          for (const settings of membershipBatch) {
            const currentMembershipCategoryId = settings['spark_current_membership_category_id']
            const currentMembershipQuestionId = settings['spark_current_membership_question_id']
            const currentMembershipQuestionInstanceId = settings['spark_current_membership_question_instance_id']
            const questionSequences = settings['question_sequences']
            const newMembershipQuestionInstance = await createNextSparkMembershipQuestionInstance(
              currentMembershipCategoryId,
              currentMembershipQuestionId,
              currentMembershipQuestionInstanceId,
              questionSequences,
              settings.owner_id,
              settings.membership_id,
            )
            if (!newMembershipQuestionInstance) {
              console.warn('Spark question instance was not created.')
              continue
            }

            output.push({
              membership_id: settings.membership_id,
              next_question: newMembershipQuestionInstance.id,
            })
          }
          return output
        }),
      ),
    )
    return results
  },
)
