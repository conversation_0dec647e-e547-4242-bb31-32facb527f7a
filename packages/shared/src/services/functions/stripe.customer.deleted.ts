import { inngest } from '../inngest'
import { updateMembershipSettings } from '@/shared-libs/prisma/membership-settings'
import { updateUserProfiles } from '@/shared-libs/prisma/user-profile'

export default inngest.createFunction(
  { id: 'customer-deleted', name: 'Customer Deleted' },
  { event: 'stripe/customer.deleted' },
  async ({ event, step }) => {
    const { data } = event
    await step.run('update-membership-settings-customer-deleted', async () => {
      if (data['isMembershipStripe']) {
        await updateUserProfiles({
          where: {
            stripe_customer_id: data['id'],
          },
          data: {
            stripe_customer_id: null,
            stripe_payment_method_id: null,
            stripe_subscription_id: null,
            stripe_subscription_status: null,
            stripe_metadata_mode: null,
          },
        })
      } else {
        await updateMembershipSettings({
          where: {
            stripe_customer_id: data['id'],
          },
          data: {
            stripe_customer_id: null,
            stripe_payment_method_id: null,
            stripe_subscription_id: null,
            stripe_subscription_status: null,
            stripe_metadata_mode: null,
          },
        })
      }
    })
  },
)
