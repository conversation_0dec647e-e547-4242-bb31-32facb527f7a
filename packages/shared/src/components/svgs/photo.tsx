import React from 'react'

const SVGPhoto: React.FC<{ width?: number; height?: number; strokeWidth?: number }> = ({
  width,
  height,
  strokeWidth,
}) => {
  return (
    <svg width={width || 16} height={height || 16} viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-671.000000, -332.000000)" fill="currentColor">
          <g transform="translate(486.000000, 316.000000)">
            <g transform="translate(175.000000, 8.000000)">
              <g transform="translate(10.000000, 8.000000)">
                <path d="M13,0 C14.6568542,0 16,1.34314575 16,3 L16,13 C16,14.6568542 14.6568542,16 13,16 L3,16 C1.34314575,16 0,14.6568542 0,13 L0,3 C0,1.34314575 1.34314575,0 3,0 L13,0 Z M13,2 L3,2 C2.44771525,2 2,2.44771525 2,3 L2,10.697 L4.18626653,7.41876181 C4.50017051,6.97929623 5.09949173,6.87371288 5.54124102,7.158983 L5.6401844,7.23177872 L10.7852568,11.5193391 L14,5.745 L14,3 C14,2.44771525 13.5522847,2 13,2 Z M4.5,6 C5.32842712,6 6,5.32842712 6,4.5 C6,3.67157288 5.32842712,3 4.5,3 C3.67157288,3 3,3.67157288 3,4.5 C3,5.32842712 3.67157288,6 4.5,6 Z"></path>
              </g>
            </g>
          </g>
        </g>
      </g>
    </svg>
  )
}

export default SVGPhoto
