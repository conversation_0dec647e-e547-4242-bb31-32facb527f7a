import Button from '@mui/material/Button'
import React, { FC, useCallback, useMemo } from 'react'

import { CLOUD_NAME, CLOUD_UPLOAD_PRESET } from '@/shared-config/envs'

export const MediaPicker: FC<{
  imageMax?: number
  imageMin?: number
  accept?: string | string[] // ['audio/*', 'video/*', 'image/*', 'text/*', 'application/*', 'image/jpeg', '.pdf'],
  buttonStr?: string
  onChange: (value: {
    access_mode: string //"public"
    asset_id: string //"1af0acba29074eaac93308d9ff5d4410"
    batchId: string //"uw-batch2"
    bytes: number
    created_at: string //"2022-04-09T20:33:05Z"
    etag: string //"73c4b3758af64736831438b028ac4524"
    existing: false
    format: string //"png"
    height: number
    id: string //"uw-file3"
    original_filename: string //"barbara"
    path: string //"v1649536385/barbara_pecfqw.png"
    placeholder: false
    public_id: string //"barbara_pecfqw"
    resource_type: string //"image"
    secure_url: string //"https://res.cloudinary.com/memberup-llc/image/upload/v1649536385/barbara_pecfqw.png"
    signature: string //"c2a31358fed0fde699ad12bd165cd57d196ab471"
    tags: string[]
    thumbnail_url: string //"https://res.cloudinary.com/memberup-llc/image/upload/c_limit,h_60,w_90/v1649536385/barbara_pecfqw.png"
    type: string //"upload"
    url: string //"http://res.cloudinary.com/memberup-llc/image/upload/v1649536385/barbara_pecfqw.png"
    version: number
    version_id: string //"1837b56fc0c275b1ade8d89fba519e84"
    width: number
  }) => void
  [key: string]: any
}> = ({ imageMax, imageMin, accept, buttonStr, onChange, ...rest }) => {
  const checkUploadResult = useCallback((error, result) => {
    if (!error && result && result.event === 'success') {
      onChange(result.info)
    }
    if (result.event === 'close') {
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  const showWidget = useCallback((widget) => widget?.open(), [])

  const widget = useMemo(
    () =>
      window['cloudinary']?.createUploadWidget(
        {
          cloudName: CLOUD_NAME,
          uploadPreset: CLOUD_UPLOAD_PRESET,
        },
        checkUploadResult,
      ),
    [checkUploadResult],
  )

  return (
    <Button onClick={() => showWidget(widget)} {...rest}>
      {buttonStr || 'Upload Image'}
    </Button>
  )
}
