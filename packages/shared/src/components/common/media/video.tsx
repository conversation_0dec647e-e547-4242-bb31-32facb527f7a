import PlayCircleFilledIcon from '@mui/icons-material/PlayCircleFilled'
import Box from '@mui/material/Box'
import CircularProgress from '@mui/material/CircularProgress'
import axios from 'axios'
import _get from 'lodash/get'
import React, { useEffect, useRef, useState } from 'react'
import ReactPlayer from 'react-player/lazy'

import { useMounted } from '../../hooks/use-mounted'
import { YOUTUBE_API_KEY } from '@/shared-config/envs'

export const AppVideo: React.FC<{
  url: string
  thumbnail?: string
  height?: number
  playing?: boolean
  canPlay?: boolean
}> = ({ url, thumbnail: thumbnailProp, height, playing, canPlay = true }) => {
  const mountedRef = useMounted(true)
  const rootEleRef = useRef(null)
  const [playerConfig, setPlayerConfig] = useState({
    width: '100%',
    height: `${height || 204}px`,
  })
  const [videoDetails, setVideoDetails] = useState({
    url: '',
    title: '',
    description: '',
    thumbnails: null,
    config: {
      youtube: {
        playerVars: { showinfo: 1 },
      },
      vimeo: {
        controls: false,
      },
    },
  })
  const [videoLoaded, setVideoLoaded] = useState(false)

  useEffect(() => {
    try {
      if (videoDetails.url === url && (videoDetails.title || videoDetails.thumbnails)) return

      if (url) {
        if (url.indexOf('youtube.com') >= 0 || url.indexOf('youtu.be') >= 0) {
          const temp = url.indexOf('youtube.com') >= 0 ? url.split('watch?v=') : url.split('youtu.be/')
          const videoId = temp[1]
          if (!videoId) return
          axios
            .get(
              `https://www.googleapis.com/youtube/v3/videos?part=snippet&id=${videoId}&fields=items(id%2Csnippet)&key=${YOUTUBE_API_KEY}`,
            )
            .then((res) => {
              if (mountedRef.current) {
                setVideoDetails({
                  url: url,
                  title: _get(res, ['data', 'items', 0, 'snippet', 'title']) || '',
                  description: _get(res, ['data', 'items', 0, 'snippet', 'description']) || '',
                  thumbnails: _get(res, ['data', 'items', 0, 'snippet', 'thumbnails']) || null,
                  config: {
                    youtube: {
                      playerVars: { showinfo: 1 },
                    },
                    vimeo: {
                      controls: false,
                    },
                  },
                })
              }
            })
            .catch((err) => {
              console.log('getVideoError ====', err)
            })
        } else if (url.indexOf('vimeo.com') >= 0) {
          const temp = url.split('vimeo.com/')
          const videoId = temp[1]
          if (!videoId) return

          axios
            .get(`https://vimeo.com/api/v2/video/${videoId}.json`, {
              headers: {
                jsonp: 'callback',
                dataType: 'jsonp',
              },
            })
            .then((res) => {
              if (mountedRef.current) {
                setVideoDetails({
                  url: url,
                  title: _get(res, ['data', 0, 'title']) || '',
                  description: _get(res, ['data', 0, 'description']) || '',
                  thumbnails: {
                    thumbnail_large: _get(res, ['data', 0, 'thumbnail_large']) || '',
                    thumbnail_medium: _get(res, ['data', 0, 'thumbnail_medium']) || '',
                    thumbnail_small: _get(res, ['data', 0, 'thumbnail_small']) || '',
                  },
                  config: {
                    youtube: {
                      playerVars: { showinfo: 1 },
                    },
                    vimeo: {
                      controls: false,
                    },
                  },
                })
              }
            })
            .catch((err) => {
              console.log('getVideoError ====', err)
            })
        }
      }
    } catch (err: any) {}
    return () => {}
  }, [url])

  useEffect(() => {
    if (!mountedRef.current) return
    setPlayerConfig({
      width: '100%',
      height: `${height || rootEleRef.current.offsetWidth * 0.56}px`,
    })
  }, [height, url, videoDetails])

  const handleReady = () => {
    setVideoLoaded(true)
  }

  const thumbnail = thumbnailProp || videoDetails?.thumbnails?.maxres?.url || videoDetails?.thumbnails?.thumbnail_large

  return (
    <Box
      ref={rootEleRef}
      sx={{
        position: 'relative',
        width: '100%',
        '&:empty': {
          display: 'none',
        },
      }}
      data-cy="app-video-wrapper"
    >
      {Boolean(url) ? (
        <>
          <Box
            sx={{
              position: 'relative',
              lineHeight: 0,
              margin: 'auto',
              width: playerConfig.width,
              height: playerConfig.height,
              pointerEvents: canPlay ? 'inherit' : 'none',
            }}
          >
            <ReactPlayer
              url={url}
              controls={true}
              playing={playing}
              onReady={() => handleReady()}
              width={playerConfig.width}
              height={playerConfig.height}
              data-cy="app-react-player"
            />
            {!videoLoaded && (
              <Box
                className="d-flex align-center justify-center"
                sx={{
                  position: 'absolute',
                  // background: 'whitesmoke',
                  left: 0,
                  top: 0,
                  width: '100%',
                  height: '100%',
                  backgroundSize: 'contain',
                  backgroundImage: thumbnail ? `url(${thumbnail})` : null,
                }}
              >
                <CircularProgress size={20} />
              </Box>
            )}
            <Box
              sx={{
                position: 'absolute',
                top: '35px',
                left: 0,
                width: 'calc(50% - 74px)',
                height: 'calc(100% - 95px)',
                opacity: 0,
                cursor: 'grab',
              }}
            ></Box>
            <Box
              sx={{
                position: 'absolute',
                top: '35px',
                right: 0,
                width: 'calc(50% - 74px)',
                height: 'calc(100% - 95px)',
                opacity: 0,
                cursor: 'grab',
              }}
            ></Box>
          </Box>
          {Boolean(videoDetails.title) && (
            <Box
              sx={{
                cursor: 'pointer',
                padding: '8px',
                textAlign: 'center',
              }}
            >
              {videoDetails.title}
            </Box>
          )}
        </>
      ) : (
        <Box
          className="align-center flex justify-center"
          sx={{
            height: '158px',
            background: 'whitesmoke',
            border: '1px solid lightgray',
          }}
        >
          <PlayCircleFilledIcon style={{ fontSize: 48 }} />
        </Box>
      )}
    </Box>
  )
}
