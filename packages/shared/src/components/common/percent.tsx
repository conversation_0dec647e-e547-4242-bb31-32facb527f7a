import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown'
import Box from '@mui/material/Box'
import useTheme from '@mui/material/styles/useTheme'
import clsx from 'clsx'
import React, { useMemo } from 'react'

import { numberToPercent } from '../../libs/numeric-utils'

export const AppPercent: React.FC<{
  currentValue: number
  lastValue: number
}> = ({ currentValue, lastValue }) => {
  const theme = useTheme()
  const percent = useMemo(() => {
    if (!currentValue && !lastValue) return '0.00%'
    return numberToPercent(100 - (Math.min(currentValue, lastValue) / Math.max(currentValue, lastValue)) * 100)
  }, [currentValue, lastValue])

  return (
    <Box
      className="d-flex align-center"
      sx={{
        borderRadius: '12px',
        color: '#fff',
        fontWeight: 'bold',
        padding: '2px',
        paddingRight: '8px',
        paddingTop: '3px',
        lineHeight: '1px',
        background: theme.palette.primary.dark,
      }}
    >
      <KeyboardArrowDownIcon className={clsx({ 'rotate-180': currentValue > lastValue })} />
      <span>{percent}</span>
    </Box>
  )
}
