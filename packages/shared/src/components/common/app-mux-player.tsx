import { useTheme } from '@mui/material'
import Box from '@mui/material/Box'
import MuxPlayer from '@mux/mux-player-react'

import '@mux/mux-player/themes/minimal'

const AppMuxPlayer = (payload: {
  asset
  className?: any
  title?: string
  viewerUserId?: string
  playerStyle
  placeholder?: any
  streamType?: any
  wrapperStyle?: any
  autoPlay?: boolean | string
  loop?: boolean
  muted?: boolean
  currentTime?: number
  volume?: number
  paused?: boolean
  src?: string | null
  poster?: string
  playbackRate?: number
  playsInline?: boolean
  preload?: string
  primaryColor?: string | null
  accentColor?: string | null
  secondaryColor?: string | null
  crossOrigin?: string
  theme?: any | null
  onCanPlay?: (e?: any) => void
  onPlay?: (e?: any) => void
  onPlaying?: (e?: any) => void
  onPause?: (e?: any) => void
  onEnded?: (e?: any) => void
  onError?: (e?: any) => void
  onResize?: (e?: any) => void
}) => {
  const {
    asset,
    className,
    title,
    viewerUserId,
    playerStyle = { width: '100%', height: '100%' },
    placeholder,
    streamType = 'on-demand',
    wrapperStyle,
    autoPlay,
    loop,
    muted,
    currentTime,
    volume,
    paused,
    src,
    poster,
    playbackRate,
    playsInline,
    preload,
    accentColor,
    primaryColor,
    crossOrigin,
    theme,
    secondaryColor,
    onCanPlay,
    onPlay,
    onPlaying,
    onPause,
    onEnded,
    onError,
    onResize,
  } = payload

  const communityTheme = useTheme()
  const playbackId = asset?.playback_ids?.[0]?.id

  return (
    <Box className={className} style={wrapperStyle || undefined} data-cy="app-mux-player" data-poster={poster}>
      {playbackId ? (
        <MuxPlayer
          className="app-mux-player"
          primaryColor={primaryColor}
          secondaryColor={secondaryColor}
          style={playerStyle}
          streamType={streamType || 'on-demand'}
          playbackId={playbackId}
          theme={theme}
          metadata={{
            video_id: asset.id,
            video_title: title || '',
            viewer_user_id: viewerUserId || 'need to set the video user',
          }}
          autoPlay={autoPlay}
          accentColor={accentColor || communityTheme.palette.primary.main}
          loop={loop}
          muted={muted}
          currentTime={currentTime}
          volume={volume}
          paused={paused}
          src={src}
          poster={poster}
          playbackRate={playbackRate}
          playsInline={playsInline}
          preload={preload}
          crossOrigin={crossOrigin}
          defaultHiddenCaptions={true}
          onCanPlay={onCanPlay}
          onPlay={onPlay}
          onPlaying={onPlaying}
          onResize={onResize}
          onPause={onPause}
          onEnded={onEnded}
          onError={onError}
        />
      ) : (
        placeholder || null
      )}
    </Box>
  )
}

AppMuxPlayer.displayName = 'AppMuxPlayer'

export default AppMuxPlayer
