import Box from '@mui/material/Box'
import { styled } from '@mui/material/styles'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import clsx from 'clsx'
import { CSSProperties, useMemo } from 'react'
import { getSuperEllipsePathAsDataUri, Preset } from 'superellipsejs'

import { AppImg } from './media/image'
import { THEME_MODE_ENUM } from '@/shared-types/enum'
import { TAppCropArea } from '@/shared-types/types'

export function getMaskStyle(
  width: number,
  height: number,
  r1: number = Preset.iOS.r1,
  r2: number = Preset.iOS.r2,
  p1?: number,
  p2?: number,
): CSSProperties {
  const w = width
  const h = height
  const { dataUri } = getSuperEllipsePathAsDataUri(
    w,
    h,
    p1 !== undefined ? p1 : r1 * Math.min(w, h),
    p2 !== undefined ? p2 : r2 * Math.min(w, h),
  )
  return {
    maskImage: `url("${dataUri}")`,
    maskPosition: 'center',
    maskRepeat: 'no-repeat',
    // maskSize: 'contain',
    WebkitMaskImage: `url("${dataUri}")`,
    WebkitMaskPosition: 'center',
    WebkitMaskRepeat: 'no-repeat',
    // WebkitMaskSize: 'contain'
  }
}

const ProfileImage = ({
  className,
  cropArea,
  cropAreaAspect,
  fontSize,
  imageUrl,
  name,
  r1 = 0.22,
  r2 = 0.5,
  size = 32,
  style: styleProp,
  showUserIcon,
  isClickable = true,
  onClick,
}: {
  className?: string
  cropArea?: TAppCropArea
  cropAreaAspect?: number
  fontSize?: number
  imageUrl: string
  name?: string
  r1?: number
  r2?: number
  size?: number
  style?: any
  showUserIcon?: boolean
  isClickable?: boolean
  onClick?: () => void
}) => {
  const theme = useTheme()
  const isDarkTheme = theme.palette.mode === THEME_MODE_ENUM.dark
  let backgroundImage = isDarkTheme
    ? 'linear-gradient(to right bottom, #E4E6E7, #DFE1E2, #DADCDE, #D6D8D9, #D1D3D5)'
    : 'linear-gradient(to right bottom, #232327, #26272B, #2A2B2F, #2D2F34, #313338)'

  const textColor = isDarkTheme ? '#2c2c2d' : '#e3e3e4'

  const renderProfileImage = useMemo(() => {
    if (imageUrl)
      return (
        <AppImg
          className="profile-image"
          src={imageUrl}
          cropArea={cropArea}
          cropAreaAspect={cropAreaAspect || 1}
          width={size}
          height={size}
          alt="Profile Picture"
        />
      )
    if (showUserIcon)
      return (
        <>
          <AppImg
            className="user-icon"
            src={`/assets/default/svgs/no-profile-image.svg`}
            width={fontSize}
            height={fontSize}
            alt="ProfileImage"
            style={{ color: '#8D94A3' }}
          />
        </>
      )
    return (
      <Typography
        className="profile-image-text"
        lineHeight={1}
        sx={{
          color: textColor,
          fontFamily: 'Graphik SemiBold !important',
          fontSize,
          mt: '2px',
        }}
      >
        {name?.charAt(0).toUpperCase() || 'N'}
      </Typography>
    )
  }, [imageUrl, cropArea, cropAreaAspect, name, fontSize, size, textColor])
  const style = useMemo(
    () => ({
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      backgroundImage: !Boolean(showUserIcon) && backgroundImage,
      backgroundColor: 'rgba(141,148,163, 0.08)',
      width: size,
      minWidth: size,
      height: size,
      ...(styleProp || {}),
      ...getMaskStyle(size, size, r1, r2),
      cursor: isClickable ? 'pointer' : 'default',
    }),
    [size, styleProp, r1, r2],
  )

  return (
    <Box className={clsx('app-image-wrapper', className)} style={style} aria-label="profile image" onClick={onClick}>
      {renderProfileImage}
    </Box>
  )
}

export const AppProfileImage = styled(ProfileImage)(({ theme }) => ({
  '& img': {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
  },
}))
