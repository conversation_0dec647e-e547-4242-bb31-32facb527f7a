import { USER_ROLE_ENUM } from '@/shared-types/enum'
import { IMembership, IUser, IUserMembership } from '@/shared-types/interfaces'

const MU_ID = process.env.NEXT_PUBLIC_MU_ID

export const getFullName = (
  firstName?: string,
  lastName?: string,
  defaultValue: string = 'No Name',
  maxLength?: number,
): string => {
  // No names provided
  if (!firstName && !lastName) return defaultValue

  let fullName = firstName && lastName ? `${firstName} ${lastName}` : `${firstName}` || `${lastName}`

  if (maxLength) {
    fullName = fullName.length > maxLength ? fullName.substring(0, maxLength - 2) + ' ...' : fullName
  }

  return fullName
}

export const getMemberFullName = (user: Partial<IUser>) => {
  return getFullName(user?.first_name, user?.last_name)
}

export const isActiveUser = (user) => {
  return user?.status === 'active'
}

export const isUserActiveAndAcceptedInCommunity = (user, membership) => {
  if (!user || !membership) {
    return false
  }

  if (user.status !== 'active') {
    return false
  }
  const result = user.user_memberships.some((um) => {
    return um.membership.slug === membership.slug && um.status === 'accepted'
  })
  return result
}
