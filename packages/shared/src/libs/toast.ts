import { toast } from 'react-toastify'

export function showToast(message, type, customSettings: any = {}) {
  const defaultSettings = {
    TOP_RIGHT: {
      style: {
        marginTop: '60px',
        right: '40px',
      },
      position: toast.POSITION.TOP_RIGHT,
    },
    BOTTOM_RIGHT: {
      position: toast.POSITION.BOTTOM_RIGHT,
    },
    // Add other positions here if needed
  }

  const position = customSettings.position || toast.POSITION.TOP_RIGHT
  const settingsForPosition = defaultSettings[position] || defaultSettings.TOP_RIGHT

  const settings = { ...settingsForPosition, ...customSettings }

  if (type === 'success') {
    toast.success(message, settings)
  } else if (type === 'error') {
    toast.error(message, settings)
  } else if (type === 'warning') {
    //toast.warning(message, settings)
  } else {
    toast.info(message, settings)
  }
}
