import { MEMBERUP_PLANS } from '@/shared-settings/plans'
import { MEMBERUP_PLAN_ENUM, VISIBILITY_ENUM } from '@/shared-types/enum'
import { IChannel, IMembershipSetting } from '@/shared-types/interfaces'

export const COMPLETED_MEMBERSHIP_OK = 3

export const checkSpaceRestriction = (membershipSetting: IMembershipSetting, channels: IChannel[]): boolean => {
  const currentPlan = membershipSetting.plan || MEMBERUP_PLAN_ENUM.basic
  const planSettings = MEMBERUP_PLANS.find((p) => p.name === currentPlan && !p.promotional)
  return channels.length >= planSettings.maxNumOfSpaces
}

export const isPublicCommunity = (membershipSetting: Partial<IMembershipSetting>): boolean => {
  return membershipSetting.visibility === VISIBILITY_ENUM.public
}

export const getCommunityPricing = (membershipSetting: Partial<IMembershipSetting>) => {
  return membershipSetting?.stripe_prices?.find((price) => price.active)
}

export const hasSpaceLimitBeenReached = (planName: string, channelCount: number): boolean => {
  const currentPlanName = planName ?? MEMBERUP_PLAN_ENUM.basic
  const plan = MEMBERUP_PLANS.find((p) => p.name === currentPlanName && !p.promotional)
  if (!plan) {
    return true
  }
  return channelCount > plan.maxNumOfSpaces
}
