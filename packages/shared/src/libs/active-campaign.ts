import axios from 'axios'

export const activeCampaignAddContact = async (
  apiUrl: string,
  apiKey: string,
  email: string,
  firstName: string,
  lastName: string,
  fieldValues?: { field: string; value: any }[],
) => {
  try {
    const payload = {
      contact: { email, firstName, lastName },
    }
    if (fieldValues) {
      payload.contact['fieldValues'] = fieldValues
    }

    const result = await axios.post(`${apiUrl}/api/3/contact/sync`, payload, {
      headers: {
        'Api-Token': apiKey,
      },
    })
    return result.data
  } catch (err: any) {
    throw err
  }
}

export const activeCampaignUpdateContact = async (
  apiUrl: string,
  apiKey: string,
  id: string,
  email: string,
  firstName: string,
  lastName: string,
  fieldValues?: { field: string; value: any }[],
) => {
  try {
    const payload = {
      contact: { email, firstName, lastName },
    }
    if (fieldValues) {
      payload.contact['fieldValues'] = fieldValues
    }

    const result = await axios.put(`${apiUrl}/api/3/contacts/${id}`, payload, {
      headers: {
        'Api-Token': apiKey,
      },
    })
    return result.data
  } catch (err: any) {
    throw err
  }
}

export const activeCampaignDeleteContact = async (apiUrl: string, apiKey: string, id: string) => {
  try {
    await axios.delete(`${apiUrl}/api/3/contacts/${id}`, {
      headers: {
        'Api-Token': apiKey,
      },
    })
    return true
  } catch (err: any) {
    console.log('err ===', err)
    return false
  }
}

export const activeCampaignAddContactToList = async (
  apiUrl: string,
  apiKey: string,
  listId: number,
  contactId: number,
) => {
  try {
    const result = await axios.post(
      `${apiUrl}/api/3/contactLists`,
      {
        contactList: {
          list: listId,
          contact: contactId,
          status: 1,
        },
      },
      {
        headers: {
          'Api-Token': apiKey,
        },
      },
    )
    return result.data
  } catch (err: any) {
    throw err
  }
}

export const activeCampaignGetContacts = async (apiUrl: string, apiKey: string, email: string) => {
  try {
    const result = await axios.get(`${apiUrl}/api/3/contacts`, {
      headers: {
        'Api-Token': apiKey,
      },
      params: {
        email,
      },
    })
    return result.data
  } catch (err: any) {
    throw err
  }
}

export const activeCampaignGetCustomFields = async (apiUrl: string, apiKey: string) => {
  try {
    const result = await axios.get(`${apiUrl}/api/3/fields`, {
      headers: {
        'Api-Token': apiKey,
      },
    })
    return result.data
  } catch (err: any) {
    throw err
  }
}

export const activeCampaignGetCustomFieldOptions = async (apiUrl: string, apiKey: string, fieldId: number) => {
  try {
    const result = await axios.get(`${apiUrl}/api/3/fields/${fieldId}/options`, {
      headers: {
        'Api-Token': apiKey,
      },
    })
    return result.data
  } catch (err: any) {
    throw err
  }
}

export const activeCampaignSyncContactData = async (
  apiUrl: string,
  apiKey: string,
  contact: {
    email: string
    fieldValues: {
      field: string
      value: string
    }[]
  },
) => {
  try {
    const result = await axios.post(
      `${apiUrl}/api/3/contact/sync`,
      { contact },
      {
        headers: {
          'Api-Token': apiKey,
        },
      },
    )
    return result.data
  } catch (err: any) {
    throw err
  }
}

export const activeCampaignGetContactFieldValues = async (apiUrl: string, apiKey: string, contactId: string) => {
  try {
    const result = await axios.get(`${apiUrl}/api/3/contacts/${contactId}/fieldValues`, {
      headers: {
        'Api-Token': apiKey,
      },
    })
    return result.data
  } catch (err: any) {
    throw err
  }
}

export const activeCampaignCreateContactCustomFieldValue = async (
  apiUrl: string,
  apiKey: string,
  contactId: string,
  fieldId: string,
  value: string,
) => {
  try {
    const payload = {
      fieldValue: {
        contact: parseInt(contactId),
        field: parseInt(fieldId),
        value,
      },
      useDefaults: true,
    }

    const result = await axios.post(`${apiUrl}/api/3/fieldValues`, payload, {
      headers: {
        'Api-Token': apiKey,
      },
    })
    return result.data
  } catch (err: any) {
    throw err
  }
}

export const activeCampaignUpdateContactCustomFieldValue = async (
  apiUrl: string,
  apiKey: string,
  contactId: string,
  fieldId: string,
  fieldValueId: string,
  value: string,
) => {
  try {
    const payload = {
      fieldValue: {
        contact: parseInt(contactId),
        field: parseInt(fieldId),
        value,
      },
      useDefaults: true,
    }

    const result = await axios.put(`${apiUrl}/api/3/fieldValues/${fieldValueId}`, payload, {
      headers: {
        'Api-Token': apiKey,
      },
    })
    return result.data
  } catch (err: any) {
    throw err
  }
}
