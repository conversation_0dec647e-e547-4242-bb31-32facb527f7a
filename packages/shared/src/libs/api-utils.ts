import { EVENT_STATUS_ENUM, USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'

const idFields = ['question_id']

export const status200 = (res, data: any) => {
  return res.status(200).json({
    success: true,
    data,
  })
}

export const status201 = (res, data: any) => {
  return res.status(201).json({
    success: true,
    data,
  })
}

export const status403 = (res, message) => {
  return res.status(403).json({
    success: false,
    message,
  })
}

export const status400 = (res, message) => {
  return res.status(400).json({
    success: false,
    message,
  })
}

export const status500 = (res, message) => {
  return res.status(500).json({
    success: false,
    message,
  })
}

export const parseQuery = (query: {
  [key: string]: string | string[]
}): { where: any; take?: number; select?: any; skip?: number; orderBy: any } => {
  const { where, take, select, skip, orderBy } = query
  const newFilter = parseObject(where) || {}
  for (const idField of idFields) {
    if (typeof newFilter[idField] !== 'undefined' && !newFilter[idField]) {
      delete newFilter[idField]
    }
  }

  return {
    where: newFilter,
    take: take ? parseInt(take as string) : undefined,
    select: parseObject(select),
    skip: skip ? parseInt(skip as string) : undefined,
    orderBy: parseObject(orderBy),
  }
}

export const parseObject = (obj: string | string[]): any => {
  if (!obj) return undefined
  return typeof obj === 'string' ? JSON.parse(obj) : obj
}
