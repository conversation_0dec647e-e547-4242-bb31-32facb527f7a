import { findChannelById } from '@/shared-libs/prisma/channel'
import { prisma } from '@/shared-libs/prisma/prisma'
import { findUserById } from '@/shared-libs/prisma/user'
import { findUserProfile } from '@/shared-libs/prisma/user-profile'
import { getFullName } from '@/shared-libs/profile'
import { IChannel, IFeed, IUser } from '@/shared-types/interfaces'

const stripHtml = (htmlString) => {
  return htmlString.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ')
}

export const transformFeedToSearchObject = async (data: IFeed) => {
  try {
    let user = data.user as IUser
    let channel = data.channel as IChannel
    if (!channel) {
      channel = await findChannelById(data.channel_id)
    }
    if (!user?.profile) {
      user = await findUserById({
        where: {
          id: data.user_id,
        },
        include: {
          profile: true,
        },
      })
    }

    let text = stripHtml(data.text).trim()
    // replacing mentions
    text = text.replace(/{{mention:all}}/g, '@everyone').trim()
    const users = await prisma.user.findMany({ where: { membership_id: data.user.membership_id } })

    const userMap = users.reduce((acc, user) => {
      acc[user.id] = user
      return acc
    })

    const mentionPattern = /{{mention:user:(\w+)}}/g

    let match

    while ((match = mentionPattern.exec(text)) !== null) {
      const userId = match[1]
      const user = userMap[userId]
      if (user) {
        text = text.replace(match[0], `@${user.first_name.trim()} ${user.last_name.trim()}`).trim()
      }
    }

    let featuredThumbnail = undefined
    // getting search result featured thumbnail
    if (data.attachments?.length > 0) {
      const featuredAttachmentVideo = data.attachments.filter((attachment) => attachment.mimetype === 'video')
      if (featuredAttachmentVideo?.length > 0) {
        if (featuredAttachmentVideo?.[0]?.thumbnail?.startsWith('http')) {
          featuredThumbnail = featuredAttachmentVideo[0].thumbnail
        }
      }

      if (!featuredThumbnail) {
        const featuredAttachmentImage = data.attachments.filter((attachment) => attachment.mimetype === 'image')
        if (featuredAttachmentImage?.length > 0) {
          if (featuredAttachmentImage?.[0]?.url?.startsWith('http')) {
            featuredThumbnail = featuredAttachmentImage[0].url
          }
        }
      }

      if (!featuredThumbnail) {
        const featuredAttachmentGif = data.attachments.filter((attachment) => attachment.mimetype === 'gif')
        if (featuredAttachmentGif?.length > 0) {
          if (featuredAttachmentGif?.[0]?.embed_url?.startsWith('http')) {
            featuredThumbnail = featuredAttachmentGif[0].embed_url
          }
        }
      }
    }

    return {
      title: data.title,
      text: text,
      author_full_name: getFullName(user.first_name, user.last_name),
      author_id: user.id,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      objectID: data.id,
      permalink: data.permalink?.trim(),
      channel: data.channel.name.trim(),
      post_pic_url: featuredThumbnail,
      feed_status: data.feed_status.trim(),
      viewable_by: channel.membership_id || null,
    }
  } catch (err) {
    return null
  }
}

export const transformLibraryToSearchObject = async (data: any) => {
  try {
    let user = data.user
    if (!user) {
      user = await findUserById({
        where: {
          id: data.user_id,
        },
        include: {
          profile: true,
        },
      })
    }

    return {
      objectID: data.id,
      title: data.title,
      text: data.text,
      visibility: data.visibility,
      type: data.type,
      thumbnail_url: data.thumbnail_url,
      course_id: data.content_library_course_id,
      user: {
        id: user.id,
        first_name: user.first_name,
        last_name: user.last_name,
        name: getFullName(user.first_name, user.last_name),
        image: user.profile?.image || user.image,
      } as IUser,
      createdAt: data.createdAt,
      updatedAt: data.updatedAt,
      viewable_by: data.membership_id || null,
    }
  } catch (err) {
    return null
  }
}

export const transformFeedsToSearchObjects = async (data: IFeed[]) => {
  const temp = []
  for (const item of data) {
    const temp1 = await transformFeedToSearchObject(item)
    if (temp1) {
      temp.push(temp1)
    }
  }
  return temp
}
