import { Prisma } from '@prisma/client'

import prisma, { mapNullToJsonNull } from './prisma'
import { IMembershipSetting } from '@/shared-types/interfaces'

const jsonFields = [
  'active_campaign',
  'custom_host_cert',
  'emails',
  'onboarding',
  'logo_crop_area',
  'library',
  'affiliate',
  'signin',
  'signup',
  'signup_payment',
  'spark_expire_time',
  'stripe_prices',
  'stripe_connect_account',
]

export async function createMembershipSetting(payload: Prisma.MembershipSettingCreateArgs) {
  const result = await prisma.membershipSetting.create(payload)
  return result as IMembershipSetting
}

export async function findMembershipSettingById(membershipId: string) {
  const result = await prisma.membershipSetting.findFirst({ where: { id: membershipId } })
  return result as IMembershipSetting
}

export async function findMembershipSetting(payload: Prisma.MembershipSettingFindFirstArgs) {
  const result = await prisma.membershipSetting.findFirst(payload)
  return result as IMembershipSetting
}

export async function findDistinctMembershipSettingsTimeZones() {
  const results: any[] =
    await prisma.$queryRaw`SELECT distinct ms.time_zone FROM membership_settings ms INNER JOIN memberships m on m.id = ms.membership_id where m.active = 1`
  return results.map((t) => t.time_zone)
}

export async function findMembershipSettings(payload: Prisma.MembershipSettingFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }
  const result = await prisma.membershipSetting.findMany(args)
  const total = await prisma.membershipSetting.count({ where })
  return { docs: result as IMembershipSetting[], total }
}

export async function updateMembershipSetting(payload: Prisma.MembershipSettingUpdateArgs) {
  mapNullToJsonNull(jsonFields, payload.data)
  const result = await prisma.membershipSetting.update(payload)
  return result as IMembershipSetting
}

export async function updateMembershipSettings(payload: Prisma.MembershipSettingUpdateManyArgs) {
  mapNullToJsonNull(jsonFields, payload.data)
  const result = await prisma.membershipSetting.updateMany(payload)
  return result
}
