import { Prisma } from '@prisma/client'

import prisma, { mapNullToJsonNull } from './prisma'
import { IUserProfile } from '@/shared-types/interfaces'

const jsonFields = ['enable_notifications', 'getting_started', 'image_crop_area', 'relationships', 'affiliate']

export async function createUserProfile(payload: Prisma.UserProfileCreateArgs) {
  const result = await prisma.userProfile.create(payload)
  return result as IUserProfile
}

export async function findUserProfileById(id: string) {
  const result = await prisma.userProfile.findUnique({
    where: {
      id,
    },
  })
  return result as IUserProfile
}

export async function findUserProfile(payload: Prisma.UserProfileFindFirstArgs) {
  const result = await prisma.userProfile.findFirst(payload)
  return result as IUserProfile
}

export async function updateUserProfile(payload: Prisma.UserProfileUpdateArgs) {
  mapNullToJsonNull(jsonFields, payload.data)
  const result = await prisma.userProfile.update(payload)
  return result as IUserProfile
}

export async function updateUserProfiles(payload: Prisma.UserProfileUpdateManyArgs) {
  mapNullToJsonNull(jsonFields, payload.data)
  const result = await prisma.userProfile.updateMany(payload)
  return result
}
