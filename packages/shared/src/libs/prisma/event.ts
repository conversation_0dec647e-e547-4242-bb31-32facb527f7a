import { Prisma } from '@prisma/client'

import prisma, { mapNullToJsonNull } from './prisma'
import { IEvent } from '@/shared-types/interfaces'

const jsonFields = ['location_map_center']

export async function createEvent(payload: Prisma.EventCreateArgs) {
  const result = await prisma.event.create(payload)
  return result as IEvent
}

export async function findEventById(payload: Prisma.EventFindUniqueArgs) {
  const result = await prisma.event.findUnique(payload)
  return result as IEvent
}

export async function findEvent(payload: Prisma.EventFindFirstArgs) {
  const result = await prisma.event.findFirst(payload)
  return result as IEvent
}

export async function findEvents(payload: Prisma.EventFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }

  const result = await prisma.event.findMany(args)
  const total = await prisma.event.count({ where })
  return { docs: result as IEvent[], total }
}

export async function updateEvent(payload: Prisma.EventUpdateArgs) {
  mapNullToJsonNull(jsonFields, payload.data)
  const result = await prisma.event.update(payload)
  return result as IEvent
}

export async function updateEvents(payload: Prisma.EventUpdateManyArgs) {
  mapNullToJsonNull(jsonFields, payload.data)
  const result = await prisma.event.updateMany(payload)
  return result
}

export async function deleteEventById(id: string) {
  const result = await prisma.event.delete({
    where: {
      id,
    },
  })
  return result as IEvent
}
