import { Prisma } from '@prisma/client'

import prisma from './prisma'
import { ITag } from '@/shared-types/interfaces'

export async function createTag(payload: Prisma.TagCreateArgs) {
  const result = await prisma.tag.create(payload)
  return result as ITag
}

export async function findTagById(id: string) {
  const result = await prisma.tag.findUnique({
    where: {
      id,
    },
  })
  return result as ITag
}

export async function findTags(payload: Prisma.TagFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }

  const result = await prisma.tag.findMany(args)
  const total = await prisma.tag.count({ where })
  return { docs: result as ITag[], total }
}

export async function updateTag(payload: Prisma.TagUpdateArgs) {
  const result = await prisma.tag.update(payload)
  return result as ITag
}

export async function updateTags(payload: Prisma.TagUpdateManyArgs) {
  const result = await prisma.tag.updateMany(payload)
  return result
}

export async function deleteTagById(id: string) {
  const result = await prisma.tag.delete({
    where: {
      id,
    },
  })
  return result as ITag
}
