import prisma from './prisma'

export const findContentLibraryById = async (contentLibraryId: string) => {
  const contentLibrary = await prisma.contentLibrary.findUnique({
    where: {
      id: contentLibraryId,
    },
  })
  return contentLibrary
}

export const findContentLibraryByMembershipId = async (membership_id: string) => {
  const contentLibrary = await prisma.contentLibrary.findFirst({
    where: {
      membership_id: membership_id,
    },
  })
  return contentLibrary
}

export async function createContentLibrary({ title, description, thumbnail, metadata, membership_id, user_id }) {
  const newContentLibrary = await prisma.contentLibrary.create({
    data: {
      title,
      description,
      thumbnail,
      metadata,
      membership: {
        connect: { id: membership_id },
      },
    },
  })

  return newContentLibrary
}

async function createDefaultLesson(contentLibrary, contentLibraryCourse, contentLibraryCourseSection, membershipId) {
  await prisma.contentLibraryCourseLesson.create({
    data: {
      title: 'New Lesson',
      text: '',
      type: 'text',
      sequence: 0,
      visibility: 'draft',
      membership_id: membershipId,
      section_id: contentLibraryCourseSection.id,
      content_library_id: contentLibrary.id,
      content_library_course_id: contentLibraryCourse.id,
      resource_files: [],
    },
  })
}

export const createDefaultLibraryAndCourse = async (membershipId) => {
  const existingContentLibrary = await prisma.contentLibrary.findFirst({
    where: { membership_id: membershipId },
  })

  if (!existingContentLibrary) {
    const contentLibrary = await prisma.contentLibrary.create({
      data: {
        title: 'Your title',
        description: 'Your description',
        membership_id: membershipId,
      },
    })

    const existingLibraryCourse = await prisma.contentLibraryCourse.findFirst({
      where: { membership_id: membershipId },
    })

    if (!existingLibraryCourse) {
      const contentLibraryCourse = await prisma.contentLibraryCourse.create({
        data: {
          title: 'Your course title',
          description: 'Your course description',
          visibility: 'draft',
          membership_id: membershipId,
          content_library_id: contentLibrary.id,
        },
      })

      // Update the contentLibrary metadata field
      await prisma.contentLibrary.update({
        where: { id: contentLibrary.id },
        data: {
          metadata: {
            course_order: [contentLibraryCourse.id],
          },
        },
      })

      const contentLibraryCourseSection = await prisma.contentLibraryCourseSection.create({
        data: {
          name: 'New Section',
          sequence: 0,
          content_library_course_id: contentLibraryCourse.id,
          membership_id: membershipId,
          content_library_id: contentLibrary.id,
        },
      })

      await createDefaultLesson(contentLibrary, contentLibraryCourse, contentLibraryCourseSection, membershipId)
    }
  }
}

export const updateCourseCompletionPercentage = async (courseId: string, prismaTransactionInstance?) => {
  let prismaInstance = prisma
  if (prismaTransactionInstance) {
    prismaInstance = prismaTransactionInstance
  }
  const users = await prismaInstance.contentLibraryCourseUserProgress.findMany({
    where: {
      content_library_course_id: courseId,
    },
    select: {
      user_id: true,
      done: true,
    },
  })

  // First, get the IDs of the sections with visibility: 'published'
  const sections = await prismaInstance.contentLibraryCourseSection.findMany({
    where: {
      visibility: 'published',
    },
    select: {
      id: true,
    },
  })

  const sectionIds = sections.map((section) => section.id)

  // Then, use these IDs in your ContentLibraryCourseLesson query
  const totalPublishedLessons = await prismaInstance.contentLibraryCourseLesson.findMany({
    where: {
      content_library_course_id: courseId,
      section_id: {
        in: sectionIds,
      },
      visibility: 'published',
    },
    select: {
      id: true,
    },
  })
  // update the progress for each user

  for (const user of users) {
    const done = user.done
    const userId = user.user_id
    const completedLessons = Object.keys(done).filter((lessonId) => {
      return totalPublishedLessons.find((lesson) => lesson.id === lessonId)
    })

    const percentage =
      totalPublishedLessons.length > 0 ? (completedLessons.length / totalPublishedLessons.length) * 100 : 0
    await prismaInstance.contentLibraryCourseUserProgress.update({
      where: {
        content_library_course_id_user_id: {
          user_id: userId,
          content_library_course_id: courseId,
        },
      },
      data: {
        progress_percentage: percentage,
      },
    })
  }
}
