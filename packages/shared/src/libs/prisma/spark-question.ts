import { Prisma } from '@prisma/client'

import prisma from './prisma'
import { ISparkQuestion } from '@/shared-types/interfaces'

export async function createSparkQuestion(payload: Prisma.SparkQuestionCreateArgs) {
  const result = await prisma.sparkQuestion.create(payload)
  return result as ISparkQuestion
}

export async function markSparkMembershipQuestionInstanceAsCompleted(sparkMembershipQuestionInstanceId: string) {
  const result = await prisma.sparkMembershipQuestionInstance.update({
    where: { id: sparkMembershipQuestionInstanceId },
    data: {
      state: 'completed',
    },
    select: {
      id: true,
    },
  })
  return result
}

export async function findMembershipsWithSparkEnabled(): Promise<any[]> {
  return await prisma.$queryRaw`SELECT
                                  m.id as membership_id,
                                  ms.time_zone,
                                  ms.spark_current_membership_category_id,
                                  ms.spark_current_membership_question_instance_id,
                                  ms.spark_current_membership_question_id,
                                  smc.question_sequences,
                                  um.id as owner_id
                                FROM memberships m
                                       INNER JOIN membership_settings ms ON ms.membership_id = m.id
                                       INNER JOIN spark_membership_categories smc ON ms.spark_current_membership_category_id = smc.id
                                       INNER JOIN user_memberships um ON um.membership_id = m.id
                                WHERE
                                  ms.spark_enabled = true
                                  AND ms.spark_current_membership_category_id is not null
                                  AND um.user_role = 'owner'
                                  AND m.active = true`
}

export async function findMembershipSparkSettings(membershipId: string) {
  const results =
    await prisma.$queryRaw`SELECT ms.active_spark_category_id, smc.id as m_category_id, smc.question_sequences, smc.active_question_id, smc.active_m_question_id, smc.id as membership_category_id, sq.*
  FROM membership_settings ms
  INNER JOIN spark_categories sc ON ms.active_spark_category_id = sc.id
  INNER JOIN spark_m_categories smc ON smc.category_id = sc.id
  LEFT JOIN spark_questions sq ON smc.active_question_id = sq.id
  WHERE ms.membership_id = ${membershipId}
  AND smc.membership_id = ${membershipId}`
  return results?.[0]
}

export async function findCurrentSparkMembershipQuestion(membershipId: string) {
  const result = await prisma.$queryRaw`
  SELECT
      smqi.id as id,
      smqi.content,
      smqi.createdAt,
  FROM memberships m
  INNER JOIN membership_settings ms on ms.membership_id = m.id
  INNER JOIN spark_membership_categories smc ON smc.category_id = ms.spark_current_membership_category_id
  INNER JOIN spark_membership_question_instances smqi on ms.spark_current_membership_question_instance_id = smq.id
  WHERE smqi.active = true AND m.id = ${membershipId}`
  return result?.[0]
}

export async function findSparkMembershipQuestions(membershipId: string, categoryId: string): Promise<any[]> {
  return prisma.sparkMembershipQuestion.findMany({
    where: {
      spark_membership_category_id: categoryId,
      membership_id: membershipId,
    },
    orderBy: { createdAt: 'asc' },
  })
}

export async function findSparkQuestionWithAppliedSettings(membershipId: string, questionId: string): Promise<any[]> {
  const result = await prisma.$queryRaw`
      SELECT 
          sq.id,
          COALESCE(sqs.content, sq.content) AS content,
          COALESCE(sqs.answer, sq.answer) AS answer,
          COALESCE(sqs.active, sq.active) AS active,
          sqs.id as question_settings_id,
          sq.seed_id,
          sq.createdAt,
          sq.updatedAt,
          sq.category_id,
          sq.membership_id
      FROM 
          spark_questions sq
      LEFT JOIN 
          spark_questions_settings sqs ON sq.id = sqs.question_id
      WHERE
          (sqs.membership_id = ${membershipId} OR sqs.membership_id IS NULL) AND 
          sq.id = ${questionId};`
  return result?.[0]
}

export async function setCurrentSparkMembershipQuestionInstance(
  categoryId: string,
  activeQuestionId: string,
  activeMembershipQuestionId: string,
  questionSequences: string[],
) {
  return await prisma.sparkMembershipCategory.update({
    where: {
      id: categoryId,
    },
    data: {
      active_question_id: activeQuestionId,
      active_m_question_id: activeMembershipQuestionId,
      question_sequences: questionSequences,
    },
  })
}
