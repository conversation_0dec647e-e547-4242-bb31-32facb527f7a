import { Action } from '@prisma/client'
import * as Sentry from '@sentry/nextjs'

import { getCurrentDateDayDateObject, getDayDateObject } from '@memberup/shared/src/libs/date-utils'
import prisma from '@/shared-libs/prisma/prisma'

export const deleteActionHistory = async (userId: string, action: Action, resourceId: string) => {
  try {
    // Throws an exception if not found
    const deletedActionHistory = await prisma.actionHistory.delete({
      where: {
        user_id_action_id_resource_id: {
          user_id: userId,
          action_id: action.id,
          resource_id: resourceId,
        },
      },
    })

    const day = getDayDateObject(new Date(deletedActionHistory.createdAt))

    const actionPerDay = await prisma.actionsPerDay.findUnique({
      where: {
        user_id_day: {
          user_id: userId,
          day: day,
        },
      },
    })

    if (!actionPerDay || actionPerDay.count === 0) {
      Sentry.captureException(new Error('Action count is already 0'), {
        extra: {
          userId,
          day,
          actionPerDay,
        },
      })
      return
    } else {
      await prisma.actionsPerDay.update({
        where: {
          user_id_day: {
            user_id: userId,
            day: day,
          },
        },
        data: {
          count: {
            decrement: 1,
          },
        },
      })
    }
  } catch (e) {
    if (e.code !== 'P2025') {
      throw e
    }
  }
}

export const createActionHistory = async (userId: string, action: Action, resourceId: string) => {
  if (resourceId !== null) {
    const actionHistory = await prisma.actionHistory.findFirst({
      where: {
        user_id: userId,
        action_id: action.id,
        resource_id: resourceId,
      },
    })

    if (actionHistory) return
  }

  await prisma.actionHistory.create({
    data: {
      user_id: userId,
      action_id: action.id,
      resource_id: resourceId,
    },
  })

  const day = getCurrentDateDayDateObject()

  await prisma.actionsPerDay.upsert({
    where: {
      user_id_day: {
        user_id: userId,
        day: day,
      },
    },
    create: {
      user_id: userId,
      day: day,
      count: 1,
    },
    update: {
      count: {
        increment: 1,
      },
    },
  })
}
