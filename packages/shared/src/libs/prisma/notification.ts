import { Prisma } from '@prisma/client'

import prisma from './prisma'
import { INotification } from '@/shared-types/interfaces'

export async function createNotification(payload: Prisma.NotificationCreateArgs) {
  const result = await prisma.notification.create(payload)
  return result as INotification
}

export async function findNotificationById(id: string) {
  const result = await prisma.notification.findUnique({
    where: {
      id,
    },
  })
  return result as INotification
}

export async function findNotifications(payload: Prisma.NotificationFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }

  const result = await prisma.notification.findMany(args)
  const total = await prisma.notification.count({ where })
  return { docs: result as INotification[], total }
}

export async function updateNotification(payload: Prisma.NotificationUpdateArgs) {
  const result = await prisma.notification.update(payload)
  return result as INotification
}

export async function updateNotifications(payload: Prisma.NotificationUpdateManyArgs) {
  const result = await prisma.notification.updateMany(payload)
  return result
}

export async function deleteNotificationById(id: string) {
  const result = await prisma.notification.delete({
    where: {
      id,
    },
  })
  return result as INotification
}

export async function deleteNotifications(where) {
  const result = await prisma.notification.deleteMany({
    where,
  })
  return result
}
