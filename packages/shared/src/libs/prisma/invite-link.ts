import { Prisma } from '@prisma/client'

import prisma from './prisma'
import { IInviteLink } from '@/shared-types/interfaces'

export async function createInviteLink(payload: Prisma.InviteLinkCreateArgs) {
  const result = await prisma.inviteLink.create(payload)
  return result as IInviteLink
}

export async function findInviteLinkById(id: string) {
  const result = await prisma.inviteLink.findUnique({
    where: {
      id,
    },
  })
  return result as IInviteLink
}

export async function findInviteLinks(payload: Prisma.InviteLinkFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }

  const result = await prisma.inviteLink.findMany(args)
  const total = await prisma.inviteLink.count({ where })
  return { docs: result as IInviteLink[], total }
}

export async function findNumOfInviteLinks(payload: Prisma.InviteLinkFindManyArgs) {
  const { where, skip, take } = payload
  const args = { where }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }
  const total = await prisma.inviteLink.count(args)
  return total
}

export async function updateInviteLink(payload: Prisma.InviteLinkUpdateArgs) {
  const result = await prisma.inviteLink.update(payload)
  return result as IInviteLink
}

export async function updateInviteLinks(payload: Prisma.InviteLinkUpdateManyArgs) {
  const result = await prisma.inviteLink.updateMany(payload)
  return result
}

export async function deleteInviteLinkById(id: string) {
  const result = await prisma.inviteLink.delete({
    where: {
      id,
    },
  })
  return result as IInviteLink
}

export async function deleteInviteLinks(where) {
  const result = await prisma.inviteLink.deleteMany({
    where,
  })
  return result
}
