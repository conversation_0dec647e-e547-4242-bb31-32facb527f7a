import { Prisma } from '@prisma/client'

import { createActionHistory, deleteActionHistory } from './actions'
import prisma from './prisma'
import { IUser } from '@memberup/shared/src/types/interfaces'
import { createStreamReaction, deleteStreamReaction } from '@/shared-libs/stream-chat'
import { ACTION_NAME_ENUM, CHANNEL_TYPE_ENUM } from '@/shared-types/enum'
import { IReaction } from '@/shared-types/interfaces'

export async function handleUserAction(
  actor: IUser,
  actionType: 'like' | 'unlike',
  awardedUserId: string,
  actionName: ACTION_NAME_ENUM,
  resourceId: string,
  membershipId: string,
) {
  const action = await prisma.action.findFirst({
    where: { action_name: actionName },
  })

  if (actionType === 'like') {
    await createActionHistory(actor.id, action, resourceId)
  } else if (actionType === 'unlike') {
    await deleteActionHistory(actor.id, action, resourceId)
  }
}

const upsertStreamReaction = async (reaction: IReaction, isNew: boolean = true) => {
  const reactionData = {
    id: reaction.id,
    user_id: reaction.user_id,
    type: reaction.type,
    mentioned_users: (reaction.metadata as any)?.mentioned_users?.map((u) => u.id) || [],
  }
  if (reaction.text) {
    reactionData['text'] = reaction.text
  }

  const message = await prisma.feed.findUnique({ where: { id: reaction.message_id } })
  await createStreamReaction(CHANNEL_TYPE_ENUM.team, message.channel_id, reaction.message_id, reactionData)
}

export async function createReaction(payload: Prisma.ReactionCreateArgs, user: IUser, awardedUserId: string) {
  const newReaction = await prisma.reaction.create(payload)
  await upsertStreamReaction(newReaction)
  if (payload.data.type === 'like') {
    await handleUserAction(user, payload.data.type, awardedUserId, ACTION_NAME_ENUM.LIKE, payload.data.message_id)
  }
  return newReaction as IReaction
}

export async function findReactionById(id: string) {
  const result = await prisma.reaction.findUnique({
    where: {
      id,
    },
  })
  return result as IReaction
}

export async function findReactions(payload: Prisma.ReactionFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }

  const result = await prisma.reaction.findMany(args)
  const total = await prisma.reaction.count({ where })

  return { docs: result as IReaction[], total }
}

export async function updateReaction(payload: Prisma.ReactionUpdateArgs) {
  const result = await prisma.reaction.update(payload)
  return result as IReaction
}

export async function updateReactions(payload: Prisma.ReactionUpdateManyArgs) {
  const result = await prisma.reaction.updateMany(payload)
  return result
}

export async function deleteReactionById(id: string, operationType: string, user: IUser, awardedUserId?: string) {
  try {
    const result = await prisma.reaction.delete({
      where: {
        id,
      },
    })

    await deleteStreamReaction(result.message_id, result.type, result.user_id)

    if (operationType === 'unlike') {
      await handleUserAction(user, operationType, awardedUserId, ACTION_NAME_ENUM.LIKE, result.message_id)
    }

    return result as IReaction
  } catch (error) {
    if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
      // Handle the specific "not found" error
      return { id }
    }

    throw error
  }
}
