import { Client } from '@planetscale/database'
import { PrismaPlanetScale } from '@prisma/adapter-planetscale'
import { Prisma, PrismaClient } from '@prisma/client'

const connectionString = `${process.env.DATABASE_URL}`
const isTestEnvironment =
  process.env.NODE_ENV === 'test' || connectionString.includes('localhost') || connectionString.includes('127.0.0.1')

const prismaClientSingleton = () => {
  if (isTestEnvironment) {
    return new PrismaClient()
  }

  const client = new Client({ url: connectionString })
  const adapter = new PrismaPlanetScale(client)
  return new PrismaClient({ adapter })
}

const boostedPrismaClientSingleton = () => {
  if (isTestEnvironment) {
    return new PrismaClient()
  }

  const client = new Client({ url: connectionString })
  const adapter = new PrismaPlanetScale(client)
  const boostedPrisma = new PrismaClient({ adapter })

  boostedPrisma.$queryRaw`SET @@boost_cached_queries = true`

  return boostedPrisma
}

declare const globalThis: {
  prismaGlobal: ReturnType<typeof boostedPrismaClientSingleton>
  boostedPrismaGlobal: ReturnType<typeof boostedPrismaClientSingleton>
} & typeof global

const prisma = globalThis.prismaGlobal ?? prismaClientSingleton()
const boostedPrisma = globalThis.boostedPrismaGlobal ?? boostedPrismaClientSingleton()

export { boostedPrisma, prisma }
export default prisma

export const mapNullToJsonNull = (fields: string[], data: any) => {
  fields.forEach((f) => {
    if (data[f] === null) {
      data[f] = Prisma.JsonNull
    }
  })
}

if (process.env.NODE_ENV === 'development') {
  globalThis.prismaGlobal = prisma
  globalThis.boostedPrismaGlobal = prisma
}
