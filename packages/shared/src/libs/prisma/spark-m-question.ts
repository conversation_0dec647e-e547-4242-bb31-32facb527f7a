// @ts-nocheck
import { Prisma } from '@prisma/client'

import prisma from './prisma'
import { ISparkMembershipQuestion } from '@/shared-types/interfaces'

export async function createSparkMembershipQuestion(payload: Prisma.SparkMembershipQuestionCreateArgs) {
  const result = await prisma.sparkMembershipQuestion.create(payload)
  return result as ISparkMembershipQuestion
}

export async function findSparkMembershipQuestionById(payload: Prisma.SparkMembershipQuestionFindUniqueArgs) {
  const result = await prisma.sparkMembershipQuestion.findUnique(payload)
  return result as ISparkMembershipQuestion
}

export async function findSparkQuestionById(questionId: string) {
  const result = await prisma.sparkMembershipQuestion.findUnique(questionId)
  return result
}

export async function findSparkMembershipQuestion(payload: Prisma.SparkMembershipQuestionFindFirstArgs) {
  const result = await prisma.sparkMembershipQuestion.findFirst(payload)
  return result as ISparkMembershipQuestion
}

export async function findSparkMembershipQuestions(payload: Prisma.SparkMembershipQuestionFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }

  const result = await prisma.sparkMembershipQuestion.findMany(args)
  const total = await prisma.sparkMembershipQuestion.count({ where })
  return { docs: result as ISparkMembershipQuestion[], total }
}

export async function getSparkQuestionAppliedSettings(questionId: string, membershipId: string) {
  const result = await prisma.sparkQuestion.findFirst({
    where: { id: questionId },
  })
  return result
}

export async function createSparkQuestionSettings(payload: Prisma.SparkQuestionSettingCreateArgs) {
  const result = await prisma.sparkQuestionSetting.create(payload)
  return result
}

export async function updateSparkQuestionSettings(id: string, updateValues: any) {
  const result = await prisma.sparkQuestionSetting.update({
    where: { id: id },
    data: {
      ...updateValues,
    },
  })
  return result
}

export async function upsertSparkQuestionSettings(
  questionId: string,
  membershipId: string,
  createValues: any,
  updateValues: any,
) {
  const result = await prisma.sparkQuestionSetting.upsert({
    where: { question_id_membership_id: { question_id: questionId, membership_id: membershipId } },
    create: {
      membership_id: membershipId,
      question_id: questionId,
      ...createValues,
    },
    update: {
      ...updateValues,
    },
    select: {
      id: true,
    },
  })
  return result
}

export async function updateSparkMembershipQuestion(id: string, payload: Prisma.SparkMembershipQuestionUpdateArgs) {
  const result = await prisma.sparkMembershipQuestion.update(payload)
  return result as ISparkMembershipQuestion
}

export async function updateSparkMembershipQuestions(payload: Prisma.SparkMembershipQuestionUpdateManyArgs) {
  const result = await prisma.sparkMembershipQuestion.updateMany(payload)
  return result
}

export async function deleteSparkMembershipQuestionById(id: string) {
  const result = await prisma.sparkMembershipQuestion.delete({
    where: {
      id,
    },
  })
  return result as ISparkMembershipQuestion
}
