import { Prisma } from '@prisma/client'

import prisma from './prisma'
import { createStreamChannel, deleteStreamChannel } from '@/shared-libs/stream-chat'
import { CHANNEL_TYPE_ENUM } from '@/shared-types/enum'
import { ILive } from '@/shared-types/interfaces'

export async function createLive(payload: Prisma.LiveCreateArgs) {
  const result = (await prisma.live.create(payload)) as ILive
  await createStreamChannel(CHANNEL_TYPE_ENUM.team, result.id, {
    created_by_id: result.user_id,
    name: result.title,
    team: result.membership_id,
    custom_type: 'live',
  })
  return result
}

export async function findLiveById(payload: Prisma.LiveFindUniqueArgs) {
  const result = await prisma.live.findUnique(payload)
  return result as ILive
}

export async function findLive(payload: Prisma.LiveFindFirstArgs) {
  const result = await prisma.live.findFirst(payload)
  return result as ILive
}

export async function findLives(payload: Prisma.LiveFindManyArgs) {
  const { where, skip, take, ...rest } = payload
  const args = { where, ...rest }
  if (typeof skip !== 'undefined') {
    args['skip'] = parseInt(`${skip || 0}`)
  }
  if (typeof take !== 'undefined') {
    args['take'] = parseInt(`${take || 0}`)
  }

  const result = await prisma.live.findMany(args)
  const total = await prisma.live.count({ where })
  return { docs: result as ILive[], total }
}

export async function updateLive(payload: Prisma.LiveUpdateArgs) {
  const result = await prisma.live.update(payload)
  return result as ILive
}

export async function updateLives(payload: Prisma.LiveUpdateManyArgs) {
  const result = await prisma.live.updateMany(payload)
  return result
}

export async function deleteLiveById(id: string) {
  const result = await prisma.live.delete({
    where: {
      id,
    },
  })
  deleteStreamChannel(`${CHANNEL_TYPE_ENUM.team}:${id}`)
  return result as ILive
}
