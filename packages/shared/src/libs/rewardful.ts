import axios from 'axios'

export const getRewardfulAffiliateSSO = async (api_secret: string, id: string) => {
  try {
    const result = await axios.get(`https://api.getrewardful.com/v1/affiliates/${id}/sso`, {
      auth: {
        username: api_secret,
        password: '',
      },
    })
    return result.data || null
  } catch (err) {
    console.log(err?.response?.data)
    return null
  }
}

export const getRewardfulAffiliate = async (api_secret: string, affiliate_id: string) => {
  try {
    const result = await axios.get(`https://api.getrewardful.com/v1/affiliates/${affiliate_id}`, {
      auth: {
        username: api_secret,
        password: '',
      },
    })

    return (result && result.data) || null
  } catch (err) {
    return null
  }
}

export const createRewardfulAffiliate = async (
  api_secret: string,
  data: {
    first_name: string
    last_name: string
    email: string
    campaign_id?: string
    state?: 'active' | 'disabled' | 'suspicious'
    stripe_customer_id?: string
    token?: string
    paypal_email?: string
    wise_email?: string
  },
) => {
  try {
    const result = await axios.post(`https://api.getrewardful.com/v1/affiliates`, data, {
      auth: {
        username: api_secret,
        password: '',
      },
    })

    return (result && result.data) || null
  } catch (err) {
    console.log(err?.response?.data)
    return null
  }
}

export const updateRewardfulAffiliate = async (
  api_secret: string,
  id: string,
  data: {
    first_name?: string
    last_name?: string
    email?: string
    campaign_id?: string
    state?: 'active' | 'disabled' | 'suspicious'
    stripe_customer_id?: string
    token?: string
    paypal_email?: string
    wise_email?: string
  },
) => {
  try {
    const result = await axios.put(`https://api.getrewardful.com/v1/affiliates/${id}`, data, {
      auth: {
        username: api_secret,
        password: '',
      },
    })
    return result?.data || null
  } catch (err) {
    console.log(err?.response?.data)
    return null
  }
}

export const getRewardfulCommissions = async (
  api_secret: string,
  params: {
    affiliate_id?: string
    state?: string[] | string // ['due' | 'pending' | 'paid']
    expand?: string[] | string // ['affiliate' | 'sale']
    page?: number
    limit?: number
  },
) => {
  try {
    const result = await axios.get(`https://api.getrewardful.com/v1/commissions`, {
      auth: {
        username: api_secret,
        password: '',
      },
      params,
    })

    return result.data || null
  } catch (err) {
    console.log(err?.response?.data)
    return null
  }
}

export const getRewardfulReferrals = async (
  api_secret: string,
  params: {
    affiliate_id?: string
    conversion_state?: string[] | string // ['Visitor' | 'Lead' | 'Conversion']
    email?: string
    expand?: string[] | string // ['sale' | 'campaign']
    stripe_customer_id?: string
    page?: number
    limit?: number
  },
) => {
  try {
    const result = await axios.get(`https://api.getrewardful.com/v1/referrals`, {
      auth: {
        username: api_secret,
        password: '',
      },
      params,
    })

    return result.data || null
  } catch (err) {
    console.log(err?.response?.data)
    return null
  }
}

export const getRewardfulPayouts = async (
  api_secret: string,
  params: {
    affiliate_id?: string
    state?: string[] | string // ['due' | 'pending' | 'paid' | 'processing']
  },
) => {
  try {
    const result = await axios.get(`https://api.getrewardful.com/v1/payouts`, {
      auth: {
        username: api_secret,
        password: '',
      },
      params,
    })

    return result.data || null
  } catch (err) {
    console.log(err?.response?.data)
    return null
  }
}

export const getRewardfulAffiliateLinks = async (api_secret: string) => {
  try {
    const result = await axios.get(`https://api.getrewardful.com/v1/affiliate_links`, {
      auth: {
        username: api_secret,
        password: '',
      },
    })

    return result.data || null
  } catch (err) {
    console.log(err?.response?.data)
    return null
  }
}

export const getRewardfulAffiliateLink = async (api_secret: string, id: string) => {
  try {
    const result = await axios.get(`https://api.getrewardful.com/v1/affiliate_links/${id}`, {
      auth: {
        username: api_secret,
        password: '',
      },
    })

    return result.data || null
  } catch (err) {
    console.log(err?.response?.data)
    return null
  }
}

export const createRewardfulAffiliateLink = async (
  api_secret: string,
  data: {
    affiliate_id: string
  },
) => {
  try {
    const result = await axios.post(`https://api.getrewardful.com/v1/affiliate_links`, data, {
      auth: {
        username: api_secret,
        password: '',
      },
    })

    return result.data || null
  } catch (err) {
    console.log(err?.response?.data)
    return null
  }
}

export const updateRewardfulAffiliateLink = async (api_secret: string, id: string, token: string) => {
  try {
    const result = await axios.put(
      `https://api.getrewardful.com/v1/affiliate_links/${id}`,
      { token },
      {
        auth: {
          username: api_secret,
          password: '',
        },
      },
    )

    return result.data || null
  } catch (err) {
    console.log(err?.response?.data)
    return null
  }
}
