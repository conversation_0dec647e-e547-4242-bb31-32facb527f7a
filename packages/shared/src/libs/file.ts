enum LIBRARY_CONTENT_ENUM {
  audio = 'audio',
  doc = 'doc',
  epub = 'epub',
  image = 'image',
  pdf = 'pdf',
  ppt = 'ppt',
  video = 'video',
  other = 'other',
}

export const getFileType = (file: File) => {
  const fileType = file?.type || ''

  if (!fileType) LIBRARY_CONTENT_ENUM.other
  if (fileType.includes('video')) return LIBRARY_CONTENT_ENUM.video
  if (fileType.includes('image')) return LIBRARY_CONTENT_ENUM.image
  if (fileType.includes('audio')) return LIBRARY_CONTENT_ENUM.audio
  if (fileType.includes('pdf')) return LIBRARY_CONTENT_ENUM.pdf
  if (fileType.includes('doc')) return LIBRARY_CONTENT_ENUM.doc
  if (fileType.includes('ppt')) return LIBRARY_CONTENT_ENUM.ppt
  if (fileType.includes('epub')) return LIBRARY_CONTENT_ENUM.epub
  return LIBRARY_CONTENT_ENUM.other
}

export const getCloudinaryFileType = (fileType: string) => {
  if (!fileType) return
  if (!fileType) LIBRARY_CONTENT_ENUM.other
  /* cloudinary takes resource type for audio files as well as video */
  if (fileType.includes('video') || fileType.includes('mp3') || fileType.includes('audio')) {
    return 'video'
  } else if (fileType.includes('image')) {
    return 'image'
  } else {
    return 'raw'
  }
}

export async function getThumbnailForVideo(file: File) {
  const fileUrl = URL.createObjectURL(file)
  const video = document.createElement('video')
  const canvas = document.createElement('canvas')
  const maxWidth = 960
  const maxHeight = 960
  let videoWidth = 0
  let videoHeight = 0
  video.style.display = 'none'
  video.preload = 'metadata'
  canvas.style.display = 'none'

  // Trigger video load
  await new Promise<void>((resolve, reject) => {
    video.addEventListener('loadedmetadata', () => {
      videoWidth = video.videoWidth
      videoHeight = video.videoHeight
      if (videoWidth > maxWidth) {
        videoHeight = (video.videoHeight * maxWidth) / videoWidth
        videoWidth = maxWidth
      }
      if (videoHeight > maxHeight) {
        videoWidth = (videoWidth * maxHeight) / videoHeight
        videoHeight = maxHeight
      }

      video.width = videoWidth
      video.height = videoHeight
      canvas.width = videoWidth
      canvas.height = videoHeight
      // Seek the video to 25%
      video.currentTime = video.duration * 0.25
    })
    video.addEventListener('seeked', () => resolve())
    video.src = fileUrl
  })

  const imageUrl = await new Promise((resolve) =>
    setTimeout(() => {
      canvas.getContext('2d').drawImage(video, 0, 0, videoWidth, videoHeight)
      resolve(canvas.toDataURL('image/png'))
    }, 300),
  )
  return imageUrl as string
}

export function formatBytes(bytes: number, decimals: number = 2) {
  if (!+bytes) return '0 B'

  const k = 1024
  const dm = decimals < 0 ? 0 : decimals
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB']

  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`
}
