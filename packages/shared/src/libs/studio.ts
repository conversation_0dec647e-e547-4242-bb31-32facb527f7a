// import { STREAM_CLUB_API_KEY, STREAM_CLUB_API_SECRET } from '@/shared-config/envs'
// import axios from 'axios'

// const api = 'https://api.cloud.stream.club/v1/livestreams'

// export const createLiveStream = async () => {
//   const result = await axios.post(
//     api,
//     {},
//     {
//       auth: {
//         [username]: STREAM_CLUB_API_KEY as string,
//         password: STREAM_CLUB_API_SECRET as string,
//       },
//     }
//   )
//   return result.data
// }

// export const createLiveStreamToken = async ({
//   [username],
//   userId,
//   userType,
//   livestreamId,
// }: {
//   [username]: string
//   userId: string
//   userType: string // host
//   livestreamId: string
// }) => {
//   const result = await axios.post(
//     `${api}/${livestreamId}/token`,
//     {
//       [username],
//       userId,
//       userType,
//     },
//     {
//       auth: {
//         [username]: STREAM_CLUB_API_KEY as string,
//         password: STREAM_CLUB_API_SECRET as string,
//       },
//     }
//   )
//   return result.data
// }

// export const getLiveStream = async (livestreamId: string) => {
//   const result = await axios.get(`${api}/${livestreamId}`, {
//     auth: {
//       [username]: STREAM_CLUB_API_KEY as string,
//       password: STREAM_CLUB_API_SECRET as string,
//     },
//   })
//   return result.data
// }

// export const deleteLiveStream = async (livestreamId: string) => {
//   const result = await axios.delete(`${api}/${livestreamId}`, {
//     auth: {
//       [username]: STREAM_CLUB_API_KEY as string,
//       password: STREAM_CLUB_API_SECRET as string,
//     },
//   })
//   return result.data
// }

// export const startLiveStream = async (livestreamId: string) => {
//   const result = await axios.put(
//     `${api}/${livestreamId}/start`,
//     {},
//     {
//       auth: {
//         [username]: STREAM_CLUB_API_KEY as string,
//         password: STREAM_CLUB_API_SECRET as string,
//       },
//     }
//   )
//   return result.data
// }

// export const stopLiveStream = async (livestreamId: string) => {
//   const result = await axios.put(
//     `${api}/${livestreamId}/stop`,
//     {},
//     {
//       auth: {
//         [username]: STREAM_CLUB_API_KEY as string,
//         password: STREAM_CLUB_API_SECRET as string,
//       },
//     }
//   )
//   return result.data
// }

// export const enableLiveStreamParticipant = async (livestreamId: string) => {
//   const result = await axios.post(
//     `${api}/${livestreamId}/participants`,
//     {},
//     {
//       auth: {
//         [username]: STREAM_CLUB_API_KEY as string,
//         password: STREAM_CLUB_API_SECRET as string,
//       },
//     }
//   )
//   return result.data
// }
