import {
  addDays as dateFnsAddDays,
  addSeconds as dateFnsAddSeconds,
  differenceInSeconds as dateFnsDifferenceInSeconds,
  parse as dateFnsParse,
} from 'date-fns'
import {
  format as dateFnsTzFormat,
  formatInTimeZone as dateFnsTzFormatInTimezone,
  toDate as dateFnsTzToDate,
  utcToZonedTime as dateFnsTzUtcToZonedTime,
  OptionsWithTZ,
  zonedTimeToUtc,
} from 'date-fns-tz'
import { enUS } from 'date-fns/locale'
import { DateTime } from 'luxon'
import moment from 'moment-timezone'

import { DEFAULT_SPRK_TIMEZONE } from '../settings/spark'
import { TAppTime } from '../types/types'

export const getDateTime = ({
  timeZone,
  dateTime,
  amount,
  format,
}: {
  timeZone?: string
  dateTime?: string | number | Date
  amount?: number // seconds
  format?: string
}) => {
  let temp
  if (timeZone) {
    temp = dateTime ? dateFnsTzUtcToZonedTime(dateTime, timeZone) : dateFnsTzUtcToZonedTime(new Date(), timeZone)
  } else {
    temp = dateFnsTzToDate(dateTime || new Date())
  }

  if (amount) dateFnsAddSeconds(temp, amount)
  return format ? dateFnsTzFormat(temp, format) : temp
}

export const getDateTimeRemaining = ({
  dateTime,
  timeZone,
  dateTimeFormat,
}: {
  dateTime: Date | string | number
  timeZone?: string
  dateTimeFormat?: string
}) => {
  const current = timeZone ? dateFnsTzUtcToZonedTime(new Date(), timeZone) : new Date()
  let target
  if (timeZone) {
    target =
      typeof dateTime === 'string' && dateTimeFormat
        ? dateFnsTzUtcToZonedTime(dateFnsParse(dateTime, dateTimeFormat, new Date()), timeZone)
        : dateFnsTzUtcToZonedTime(dateTime, timeZone)
  } else {
    target = dateFnsTzToDate(dateTime || new Date())
  }

  if (target < current) {
    return null
  }
  const temp = dateFnsDifferenceInSeconds(target, current)
  return temp
}

export const getDateTimeFromNow = (dateTime) => {
  return moment(dateTime).fromNow()
}

export const formatDate = ({
  date,
  format,
  options,
}: {
  date: string | number | Date
  format?: string
  options?: OptionsWithTZ
}) => {
  let formattedDate = dateFnsTzFormat(
    date instanceof Date ? date : date === 'auto' ? new Date() : new Date(date),
    format || 'MMM dd',
    options,
  )

  if (format && format.includes('a')) {
    formattedDate = formattedDate.replace('am', 'AM').replace('pm', 'PM')
  }

  return formattedDate
}

export const formatInTimeZone = (
  date: string | number | Date,
  timeZone: string,
  format?: string,
  options?: OptionsWithTZ,
) => {
  return dateFnsTzFormatInTimezone(
    date === 'auto' || !date ? new Date().getTime() : date,
    timeZone || 'America/Los_Angeles',
    format || 'MMM dd',
    options,
  )
}

export const getOneDayBeforeInISO = (unixTime: number) => {
  // Create a Date object from Unix timestamp
  const date = new Date(unixTime)

  // Subtract timezone offset to convert to UTC
  const utcDate = new Date(date.getTime() + date.getTimezoneOffset() * 60 * 1000)

  // Subtract one day in UTC
  utcDate.setUTCDate(utcDate.getUTCDate() - 1)

  const now = new Date()
  if (utcDate.getTime() < now.getTime()) {
    // If it is, set it to current date and time plus 1 minute
    now.setMinutes(now.getMinutes() + 1)
    return now.toISOString()
  }

  // Return the date in ISO format
  return utcDate.toISOString()
}

export const zonedTimeToUtcDate = (date: string | number | Date, timeZone: string) => {
  const utcDate = zonedTimeToUtc(date, timeZone)
  return utcDate
}

export const getDuration = (start: string | number, end?: string | number) => {
  const endTime = end ? new Date(end) : new Date()
  const diff = dateFnsDifferenceInSeconds(endTime, new Date(start))
  const temp = dateFnsTzFormat(dateFnsAddSeconds(new Date(0), diff), 'HH:mm:ss')
  return temp
}

export const getExpires = (
  time: TAppTime,
  timeZone: string = DEFAULT_SPRK_TIMEZONE,
  includeSeconds: boolean = false,
) => {
  if (!time) {
    return 'Expired'
  }

  const diff: any = getDateTimeRemaining({
    dateTime: dateFnsAddDays(new Date().setHours(time.hour, time.minute, time.second, 0), 1),
    timeZone,
  })
  if (!diff || diff <= 0) {
    return 'Expired'
  }

  let temp: any = dateFnsTzFormat(dateFnsAddSeconds(new Date(0).setHours(0, 0, 0, 0), diff), 'HH:mm:ss')
  if (includeSeconds) {
    return temp
  }

  temp = temp.split(':')
  const hours = parseInt(temp[0])
  const minutes = parseInt(temp[1])
  let result = 'Expires in '
  if (hours) {
    result += `${hours} hour${hours > 1 ? 's' : ''} `
  }
  if (minutes) {
    result += `${minutes} min${minutes > 1 ? 's' : ''}`
  }
  return result
}

export const getDatesInRange = (startDate: number, endDate: number, format?: string) => {
  const date = new Date(startDate)
  const dates = []

  while (date.getTime() <= endDate) {
    dates.push(format ? formatDate({ date: new Date(date), format }) : new Date(date))
    date.setDate(date.getDate() + 1)
  }

  return dates
}

export const getTimeZoneOffset = (timeZone = 'UTC', date = new Date()) => {
  const utcDate = new Date(date.toLocaleString('en-US', { timeZone: 'UTC' }))
  const tzDate = new Date(date.toLocaleString('en-US', { timeZone }))
  return (tzDate.getTime() - utcDate.getTime()) / 36e5
}

export const formatLastActiveDate = (dateString) => {
  const options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  }
  return new Date(dateString).toLocaleDateString(undefined, options)
}
export const getTimeZoneAbbreviation = (timeZone: string) =>
  dateFnsTzFormat(dateFnsTzUtcToZonedTime(new Date(), timeZone), 'zzz', {
    timeZone,
    locale: enUS,
  })

export const getCurrentTimeZoneAbbreviation = (timeZone: string) =>
  new Date().toLocaleTimeString('en-us', { timeZoneName: 'short' }).split(' ')[2]

/*
 * @description - get formatted last active date
 * @param {string} lastActive - date string
 * @returns {string} formatted last active date
 */
export const getFormattedLastActive = (lastActive) => {
  let tempStr = ''
  const [lastActiveYear, lastActiveMonth, lastActiveDate] = formatDate({
    date: lastActive,
    format: "yyyy','L','d",
  }).split(',')
  const [nowYear, nowMonth, nowDate] = formatDate({
    date: new Date(),
    format: "yyyy','L','d",
  }).split(',')
  if (lastActiveYear === nowYear && lastActiveMonth === nowMonth && lastActiveDate === nowDate) {
    tempStr = formatDate({ date: lastActive, format: "'Today at' K:mm aaa" })
  } else if (
    lastActiveYear === nowYear &&
    lastActiveMonth === nowMonth &&
    parseInt(lastActiveDate) === parseInt(nowDate) - 1
  ) {
    tempStr = formatDate({ date: lastActive, format: "'Yesterday at' K:mm aaa" })
  } else if (lastActiveYear === nowYear) {
    tempStr = formatDate({ date: lastActive, format: "LLL d 'at' K:mm aaa" })
  } else {
    tempStr = formatDate({ date: lastActive, format: "LLL d yyyy 'at' K:mm aaa" })
  }
  if (!tempStr) return null
  return tempStr
}

export const getDayDateObject = (date: Date) => {
  // Turns provided Date object into a Date object with only the date part (time at 0 UTC & UTC timezone)
  return new Date(Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate()))
}

export const getCurrentDateDayDateObject = () => {
  return getDayDateObject(new Date())
}

export const formatEventDateTime = (event, timeZone) => {
  if (!event || !timeZone) {
    return ''
  }

  const startDate = DateTime.fromSeconds(event.start_time).setZone(timeZone)
  const startFormatted = startDate.toFormat('ccc, hh:mm a') // ccc: short weekday, MMM: month, dd: day, etc.
  const endDate = DateTime.fromSeconds(event.end_time).setZone(timeZone)
  const endFormatted = endDate.toFormat('hh:mm a ZZZZ')

  const timeRange = `${startFormatted} to ${endFormatted}`
  return timeRange
}

export const formatDateLong = (date: Date) => dateFnsTzFormat(date, "EEEE, MMMM d, yyyy 'at' hh:mm a", { locale: enUS })
