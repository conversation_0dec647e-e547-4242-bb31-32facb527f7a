import _orderBy from 'lodash/orderBy'

import { ISparkQuestion } from '../types/interfaces'

export const refineSparkQuestionSequences = (
  questions: ISparkQuestion[],
  iteratees?: string[],
  orders?: (boolean | 'asc' | 'desc')[],
  sequences?: { id: string; sequence: number }[],
  filterFunc?: (e: ISparkQuestion[]) => ISparkQuestion[],
  skip?: number,
  take?: number,
) => {
  let temp = questions.map((q) => ({
    ...q,
    sequence: sequences?.find((s) => s.id === q.id)?.sequence || q.sequence,
  }))
  if (filterFunc) {
    temp = filterFunc(temp)
  }

  if (iteratees?.length) {
    temp = _orderBy(temp, iteratees, orders)
  }

  if (skip || take) {
    temp = temp.slice(skip || 0, take || temp.length)
  }
  return temp
}
