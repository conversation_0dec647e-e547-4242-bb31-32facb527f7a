import { Knock, ListUserOptions } from '@knocklabs/node'
import { BulkSetObjectOption } from '@knocklabs/node/dist/src/resources/objects/interfaces'
import { PreferenceOptions, SetPreferencesProperties } from '@knocklabs/node/dist/src/resources/preferences/interfaces'
import { ListTenantsOptions, SetTenant } from '@knocklabs/node/dist/src/resources/tenants/interfaces'

import { cloudinaryLoader, generateCloudinaryPretransforms } from '../../../../apps/memberup/lib/cloudinary.ts'
import { KNOCK_OBJECT_ENUM } from '../types/enum.ts'
import { IUser } from '../types/interfaces.ts'
import { getFullName } from './profile.ts'

const API_KEY = process.env.KNOCK_SECRET_API_KEY

export const KNOCK_OBJECTS = [
  {
    id: KNOCK_OBJECT_ENUM.new_content,
    name: 'New Content Notification',
  },
  {
    id: KNOCK_OBJECT_ENUM.new_event,
    name: 'New Event Notification',
  },
  {
    id: KNOCK_OBJECT_ENUM.new_spark,
    name: 'New Spark Notification',
  },
  {
    id: KNOCK_OBJECT_ENUM.new_everyone_mention,
    name: 'New Everyone Mention Notification',
  },
]

export const KNOCK_OBJECT_IDS = KNOCK_OBJECTS.map((o) => o.id)

export type KnockWorkflowData =
  | {
      // content
      id: string
      community_name: string
      content_title: string
      content_url: string
      [key: string]: unknown
    }
  | {
      // event
      id: string
      community_name: string
      event_location: string
      event_start_date: string
      event_start_time: string
      event_title: string
      community_slug: string
      event_time_day_reminder: { unit: string; value: number }
      event_url: string
      event_updated: boolean
      [key: string]: unknown
    }
  | {
      // spark
      id: string
      community_name: string
      spark_question: string
      home_url: string
      [key: string]: unknown
    }
  | {
      // invite_member
      expiration_days: number | null
      community_name: string
      logo: string | null
      message?: string
      [key: string]: unknown
    }
  | {
      // verify_email
      verification_code: string
      [key: string]: unknown
    }
  | {
      // reset_password
      reset_link: string
      [key: string]: unknown
    }
  | {
      // new_member, new_member_welcome
      community_name: string
      community_url?: string
      creator_name?: string
      login_url?: string
      [key: string]: unknown
    }
  | {
      // community_name_changed
      creator_name: string
      community_name: string
      community_url: string
      [key: string]: unknown
    }
  | { [key: string]: unknown } // fallback for other workflows

export type KnockWorkflowRecipients = { id: string; name?: string; email?: string; collection?: string }[] | string[]

export async function knockSetTenant(id: string, tenantData: SetTenant) {
  const knockClient = new Knock(API_KEY)
  const result = await knockClient.tenants.set(id, tenantData)

  return result
}

export async function knockListTenants(filteringOptions: ListTenantsOptions) {
  const knockClient = new Knock(API_KEY)
  const result = await knockClient.tenants.list(filteringOptions)

  return result
}

export async function knockIdentifyUser(user: Partial<IUser>) {
  const knockClient = new Knock(API_KEY)

  const avatarURL = user.profile?.image
    ? cloudinaryLoader({
        loaderOptions: {
          src: user.profile?.image,
          width: 400,
          quality: 90,
        },
        preTransforms: generateCloudinaryPretransforms(user.profile?.image_crop_area),
      })
    : null

  return await knockClient.users.identify(user.id, {
    avatar: avatarURL,
    name: getFullName(user.first_name, user.last_name),
    email: user.email,
    role: user.role,
    image: user.profile?.image || user.image,
    image_crop_area: user.profile?.image_crop_area,
  })
}

export async function knockListUsers(options: ListUserOptions) {
  const knockClient = new Knock(API_KEY)
  const result = await knockClient.users.list(options)

  return result
}

export async function knockBulkDeleteUsers(ids: string[]) {
  const knockClient = new Knock(API_KEY)
  const result = await knockClient.users.bulkDelete(ids)

  return result
}

export async function knockDeleteUser(id: string) {
  const knockClient = new Knock(API_KEY)
  const result = await knockClient.users.delete(id)

  return result
}

export async function knockSetUserPreferences(
  userId: string,
  preferenceSet: SetPreferencesProperties,
  options?: PreferenceOptions,
) {
  const knockClient = new Knock(API_KEY)
  const result = await knockClient.users.setPreferences(userId, preferenceSet, options)

  return result
}

export async function knockAddObjects(collection: string, objects: BulkSetObjectOption[]) {
  const knockClient = new Knock(API_KEY)

  await knockClient.objects.bulkSet(collection, objects)
}

export async function knockDeleteObjects(collection: string, objectId: string[]) {
  const knockClient = new Knock(API_KEY)

  await knockClient.objects.bulkDelete(collection, objectId)
}

export async function knockBulkAddSubscriptions(collectionName: string, objects: string[], recipientIds: string[]) {
  const knockClient = new Knock(API_KEY)

  const subscriptions = objects.map((o) => ({
    id: o,
    recipients: recipientIds,
  }))
  const result = await knockClient.objects.bulkAddSubscriptions(collectionName, subscriptions)

  return result
}

export async function knockAddSubscriptions(collectionName: string, objectId: string, recipientIds: string[]) {
  const knockClient = new Knock(API_KEY)
  const result = await knockClient.objects.addSubscriptions(collectionName, objectId, {
    recipients: recipientIds,
  })

  return result
}

export async function knockDeleteObjectsAndSubscriptions(collectionName: string, userIds: string[]) {
  for (const objectId of KNOCK_OBJECT_IDS) {
    await knockDeleteSubscriptions(collectionName, objectId, userIds)
  }
  await knockDeleteObjects(collectionName, KNOCK_OBJECT_IDS)
}

export async function knockDeleteSubscriptions(collectionName: string, objectId: string, recipientIds: string[]) {
  const knockClient = new Knock(API_KEY)
  const result = await knockClient.objects.deleteSubscriptions(collectionName, objectId, {
    recipients: recipientIds,
  })

  return result
}

export async function knockBulkDeleteSubscriptions(
  collectionName: string,
  objectIds: string[],
  recipientIds: string[],
) {
  const knockClient = new Knock(API_KEY)
  for (const objectId of objectIds) {
    try {
      await knockClient.objects.deleteSubscriptions(collectionName, objectId, { recipients: recipientIds })
    } catch (error) {
      // If a community does not have some of the objects, it will throw an error
      // We don't want to block the execution if this happens
      console.warn(error)
    }
  }
}

export async function knockTriggerWorkflow(
  workflowKey: string,
  recipients: KnockWorkflowRecipients,
  data: KnockWorkflowData,
  actor?: string,
  tenant?: string,
  cancellationKey?: string,
  idempotencyRequestKey?: string,
) {
  if (!recipients || recipients.length === 0) return

  const knockClient = new Knock(API_KEY)

  return await knockClient.workflows.trigger(
    workflowKey,
    {
      actor,
      recipients,
      tenant,
      data,
      cancellationKey,
    },
    idempotencyRequestKey
      ? {
          idempotencyKey: idempotencyRequestKey,
        }
      : undefined,
  )
}

export async function knockCancelWorkflow(workflowKey: string, cancellationKey: string, recipients: string[]) {
  const knockClient = new Knock(API_KEY)
  await knockClient.workflows.cancel(workflowKey, cancellationKey, {
    recipients,
  })

  return true
}
