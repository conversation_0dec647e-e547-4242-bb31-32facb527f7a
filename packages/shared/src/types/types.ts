import { BACKGROUND_TYPE_ENUM, BIO_QUESTION_ENUM, COVER_IMAGE_TYPE_ENUM, MEMBERUP_PLAN_ENUM } from './enum'

// export type MessagingAttachmentType = {}
// export type MessagingChannelType = { demo?: string }
// export type MessagingCommandType = LiteralStringForUnion
// export type MessagingEventType = {}
// export type MessagingMessageType = {}
// export type MessagingReactionType = {}
// export type MessagingUserType = { image?: string }

export type DownloadFileHandler = (arg: TAttachment) => void

export type RemovePreviewHandler = (feed: any, attachment: TAttachment) => void
export type OpenInNewTabHandler = (url: string) => void
export type DeleteFileHandler = (feed: any, attachment: TAttachment) => void

export type MediaClickHandler = (index: number, medias: TAttachment[]) => void

export type TAttachment = {
  filename: string
  mimetype: string
  embed_url?: string
  id?: string
  is_gif?: string
  mux_asset?: TMuxAsset
  mux_upload_id?: string
  mux_upload_url?: string
  passthrough?: string
  show_preview?: boolean
  size_in_bytes?: number
  thumbnail?: string
  title?: string
  uploaded_date?: string
  url?: string
}

export type TAppTime = {
  hour: number
  minute: number
  second: number
}

export type TBioQuestion = {
  type: BIO_QUESTION_ENUM
  question: string
  description: string
  answers: string[]
}

export type TChannelDetails = {
  title: string
  description: string
}

export type TReportBy = {
  date: string
  name: string
  email: string
}

export type TActiveCampaign = {
  api_key: string
  api_url: string
  contact_list: number
}

export type TEmailNotice = {
  name: string
  enable?: boolean
  is_admin?: boolean
}

export type TOnboarding = {
  bio_questions: TBioQuestion[]
  show_onboarding_popup?: boolean
  welcome_asset?: string
  welcome_description?: string
  welcome_message?: string
  welcome_title?: string
}

export type TMembershipSettingLibrary = {
  cover_image?: string
  cover_image_crop_area?: TAppCropArea
  cover_image_type: COVER_IMAGE_TYPE_ENUM
  title?: string
}

export type TMembershipSettingSignin = {
  background_color?: string
  button_background_color?: string
  button_color?: string
  background_type?: BACKGROUND_TYPE_ENUM
  color?: string
  cover_image?: string
  logo_height?: number
  logo_width?: number
  subtitle?: string
  title?: string
}

export type TMembershipSettingSignup = {
  background_color?: string
  button_background_color?: string
  button_color?: string
  background_type?: BACKGROUND_TYPE_ENUM
  color?: string
  cover_image?: string
  logo_height?: number
  logo_width?: number
  subtitle?: string
  title?: string
  testimonial?: string
  testimonial_name?: string
  testimonial_image?: string
  testimonial_image_crop_area?: TAppCropArea
}

export type TMembershipSettingSignupPayment = {
  background_color?: string
  button_background_color?: string
  button_color?: string
  background_type?: BACKGROUND_TYPE_ENUM
  color?: string
  cover_image?: string
  logo_height?: number
  logo_width?: number
  subtitle?: string
  title?: string
  testimonial?: string
  testimonial_name?: string
  testimonial_image?: string
  testimonial_image_crop_area?: TAppCropArea
}

export type TMuxTrack = {
  type?: string
  max_width?: number
  max_height?: number
  max_frame_rate?: number
  max_channels?: number
  max_channel_layout?: string
  id?: string
  duration?: number
}

export type TMuxPlayback = {
  policy: string
  id: string
}

export type TMuxAsset = {
  id: string
  upload_id?: string
  playback_ids: TMuxPlayback[]
  master_access?: string
  max_stored_resolution?: string
  max_stored_frame_rate?: string
  mp4_support?: string
  status?: string
  test?: boolean
  tracks: TMuxTrack[]
  passthrough?: string
  duration?: number
  created_at?: number
  aspect_ratio?: string
}

export type TStripeConnectAccount = {
  access_token?: string
  livemode?: boolean
  refresh_token?: string
  scope?: string
  stripe_publishable_key?: string
  stripe_user_id?: string
  token_type?: string
  enabled?: boolean
}

export type TTaskStatus = {
  current_task: string
  completed_tasks: string[]
}

export type TSocial = {
  facebook?: string
  instagram?: string
  linkedin?: string
  website?: string
  x?: string
  youtube?: string
  tiktok?: string
}

export type TAppCropArea = {
  x: number
  y: number
  width: number
  height: number
  original_crop_area?: {
    x: number
    y: number
    width: number
    height: number
  }
  original_file?: any // File
  original_url?: string
  original_file_name?: string
}
export type TGetApiParams = {
  where?: string
  take?: number
  skip?: number
  orderBy?: string
  membershipId?: string
}
export type TCreateLiveStreamTokenPayload = {
  livestreamId: string
  userType: string
  username?: string
  userId?: string
}

export type TLiveStreamParticipantPayload = {
  livestreamId: string
  name: string
  mirror_video: boolean
  avatar: string
}

export type TConnectStripeAccountPayload = {
  accountId: string
  stripeApiKey: string
}

export type TStartMembershipPayload = {
  name: string
  paymentMethodId: string
  membershipPlan: MEMBERUP_PLAN_ENUM
  couponId: string
}

export type TError = {
  status: number
  message: string
}

export type TAffiliateSchema = {
  id: string
  first_name: string
  last_name: string
  email: string
  paypal_email: string
  wise_email: string
  sign_in_count: number
  unconfirmed_email: string
  state: string
  stripe_customer_id: string
  stripe_account_id: string
  visitors: number
  leads: number
  conversions: number
  campaign: any
  created_at: string
  updated_at: string
  links: any[]
}

export interface LibraryMetadata {
  course_order?: string[]
  /* allow other properties */
  [key: string]: any
}

export interface ContentLibrary {
  id: string
  title: string
  description: string
  metadata: LibraryMetadata
  [key: string]: any
}

export type CropArea = {
  x: number
  y: number
  width: number
  height: number
}

export type TColorPickerContrastReferenceColor = {
  color: string
  minColorContrastRatio: number
}
