export const ALGOLIA_APP_ID = process.env.NEXT_PUBLIC_ALGOLIA_APP_ID as string
export const ALGOLIA_ADMIN_API_KEY = process.env.NEXT_PUBLIC_ALGOLIA_ADMIN_API_KEY as string
export const ALGOLIA_SEARCH_API_KEY = process.env.NEXT_PUBLIC_ALGOLIA_SEARCH_API_KEY as string

export const DATABASE_URL = process.env.DATABASE_URL as string

export const CLOUD_API_KEY = process.env.NEXT_PUBLIC_CLOUD_API_KEY as string
export const CLOUD_NAME = process.env.NEXT_PUBLIC_CLOUD_NAME as string
export const CLOUD_UPLOAD_PRESET = process.env.NEXT_PUBLIC_CLOUD_UPLOAD_PRESET as string

export const GET_STREAM_API_ID = process.env.NEXT_PUBLIC_GET_STREAM_API_ID as string
export const GET_STREAM_APP_KEY = process.env.NEXT_PUBLIC_GET_STREAM_APP_KEY as string
export const GET_STREAM_APP_SECRET = process.env.GET_STREAM_APP_SECRET as string

export const YOUTUBE_API_KEY = process.env.NEXT_PUBLIC_YOUTUBE_API_KEY as string

export const VERCEL_PROJECT_ID = process.env.VERCEL_PROJECT_ID as string
export const VERCEL_TEAM_ID = process.env.VERCEL_TEAM_ID as string
export const VERCEL_MEMBERUP_TOKEN = process.env.VERCEL_MEMBERUP_TOKEN as string

export const STRIPE_LIVE_MODE = process.env.NEXT_PUBLIC_STRIPE_LIVE_MODE as string
export const STRIPE_CLIENT_ID = (
  STRIPE_LIVE_MODE === 'true' ? process.env.NEXT_PUBLIC_STRIPE_CLIENT_ID : process.env.NEXT_PUBLIC_STRIPE_CLIENT_ID_TEST
) as string
export const STRIPE_PUBLISH_KEY = (
  STRIPE_LIVE_MODE === 'true'
    ? process.env.NEXT_PUBLIC_STRIPE_PUBLISH_KEY
    : process.env.NEXT_PUBLIC_STRIPE_PUBLISH_KEY_TEST
) as string
export const STRIPE_SECRET_KEY = (
  STRIPE_LIVE_MODE === 'true' ? process.env.STRIPE_SECRET_KEY : process.env.STRIPE_SECRET_KEY_TEST
) as string
export const STRIPE_PRODUCT_ID_INSPIRED = (
  STRIPE_LIVE_MODE === 'true' ? process.env.STRIPE_PRODUCT_ID_INSPIRED : process.env.STRIPE_PRODUCT_ID_INSPIRED_TEST
) as string
export const STRIPE_PRODUCT_ID_INMOTION = (
  STRIPE_LIVE_MODE === 'true' ? process.env.STRIPE_PRODUCT_ID_INMOTION : process.env.STRIPE_PRODUCT_ID_INMOTION_TEST
) as string
export const STRIPE_PRODUCT_ID_INFINITE = (
  STRIPE_LIVE_MODE === 'true' ? process.env.STRIPE_PRODUCT_ID_INFINITE : process.env.STRIPE_PRODUCT_ID_INFINITE_TEST
) as string

export const STRIPE_PRODUCT_IDS_MAP = {
  basic: STRIPE_PRODUCT_ID_INSPIRED,
  pro: STRIPE_PRODUCT_ID_INMOTION,
  enterprise: STRIPE_PRODUCT_ID_INFINITE,
}

export const STRIPE_WEBHOOK_MEMBERUP_SECRET = (
  STRIPE_LIVE_MODE === 'true'
    ? process.env.STRIPE_WEBHOOK_MEMBERUP_SECRET
    : process.env.STRIPE_WEBHOOK_MEMBERUP_SECRET_TEST
) as string
export const STRIPE_WEBHOOK_MEMBERSHIP_SECRET = (
  STRIPE_LIVE_MODE === 'true'
    ? process.env.STRIPE_WEBHOOK_MEMBERSHIP_SECRET
    : process.env.STRIPE_WEBHOOK_MEMBERSHIP_SECRET_TEST
) as string
export const STRIPE_FREE_TRIAL_PERIOD = process.env.NEXT_PUBLIC_STRIPE_FREE_TRIAL_PERIOD as string
export const STRIPE_APPLICATION_FEE_BASIC = parseFloat(process.env.STRIPE_APPLICATION_FEE_BASIC)
export const STRIPE_APPLICATION_FEE_PRO = parseFloat(process.env.STRIPE_APPLICATION_FEE_PRO)
export const STRIPE_APPLICATION_FEE_ENTERPRISE = parseFloat(process.env.STRIPE_APPLICATION_FEE_ENTERPRISE)
export const STRIPE_INTERNAL_DISCOUNT_ID = process.env.STRIPE_INTERNAL_DISCOUNT_ID
