import { MEMBERUP_PLAN_ENUM, RECURRING_INTERVAL_ENUM, USER_PLAN_ENUM } from '../types/enum'

export const MAX_NUMBER_OF_SPACES = 500
export const MAX_NUMBER_OF_COURSES = 500

export const MEMBERUP_UNIFIED_ACCOUNT_PLANS = [
  {
    name: MEMBERUP_PLAN_ENUM.enterprise,
    recurringInterval: RECURRING_INTERVAL_ENUM.month,
    price: 99,
    save: 0,
    displayName: 'Monthly',
    trialDays: 14,
    key: 'infinite-monthly',
  },
  {
    name: MEMBERUP_PLAN_ENUM.enterprise,
    recurringInterval: RECURRING_INTERVAL_ENUM.year,
    price: 997,
    save: 191,
    displayName: 'Yearly',
    trialDays: 30,
    key: 'infinite-yearly',
  },
]

export const MEMBERUP_PLANS = [
  {
    name: MEMBERUP_PLAN_ENUM.basic,
    prorated: true,
    promotional: false,
    displayName: 'MemberUp',
    label: '',
    labelIcon: '',
    backgroundColor: 'transparent',
    backgroundHColor: '#bfbebe',
    boxShadow: '2px 2px 28px rgba(0,0,0,0.5)',
    containerBoxShadow: 'none',
    description: 'Start a simple community with the bare essentials.',
    monthlyNormalPrice: 0,
    monthlyPrice: 99,
    monthlySave: 0,
    annualNormalMonthlyPrice: 59,
    annualPrice: 588,
    annualMonthlyPrice: 49,
    annualSave: 218,
    maxNumOfSpaces: 3,
    maxNumOfCourses: 1,
    maxNumOfMembers: 1000,
    options: [
      '1 Community',
      'Course Hosting',
      'Events Calendar',
      'Direct Messages',
      'Custom Branding',
      'Payments',
      'MemberUp University',
    ],
  },
  {
    name: MEMBERUP_PLAN_ENUM.pro,
    prorated: true,
    promotional: false,
    displayName: 'In-Motion',
    label: 'MOST POPULAR',
    labelIcon: '😍',
    backgroundColor: '#a5ca85',
    backgroundHColor: '#8cb469',
    boxShadow: '2px 2px 39px rgb(165 202 133 / 50%)',
    containerBoxShadow: '2px 2px 28px rgba(0,0,0,0.5)',
    description: 'All the essential tools you need to build an extraordinary community and business.',
    monthlyNormalPrice: 119,
    monthlyPrice: 99,
    monthlySave: 20,
    annualPrice: 1188,
    annualNormalMonthlyPrice: 119,
    annualMonthlyPrice: 99,
    annualSave: 438,
    maxNumOfSpaces: 10,
    maxNumOfCourses: 3,
    maxNumOfMembers: 10000,
    options: [
      'Everything in Inspired',
      'PLUS:',
      'Multiple Admins',
      'Priority Support',
      'Up to 10 spaces',
      'Up to 10,000 members',
    ],
  },
  {
    name: MEMBERUP_PLAN_ENUM.enterprise,
    prorated: false,
    promotional: true,
    displayName: 'Infinite (70% OFF)',
    label: 'BEST FOR BRANDS',
    labelIcon: '',
    backgroundColor: '#7b51e0',
    backgroundHColor: '#7246db',
    boxShadow: 'none',
    containerBoxShadow: '2px 2px 28px rgba(0,0,0,0.5)',
    description: 'MemberUp’s most powerful plan, for growing businesses.',
    monthlyNormalPrice: 399,
    monthlyPrice: 299,
    monthlySave: 100,
    annualPrice: 997,
    annualNormalMonthlyPrice: 3588,
    annualMonthlyPrice: 299,
    annualSave: 2591,
    maxNumOfSpaces: MAX_NUMBER_OF_SPACES,
    maxNumOfCourses: MAX_NUMBER_OF_COURSES,
    maxNumOfMembers: 100000,
    options: [
      'Everything in In-Motion',
      'PLUS:',
      '1TB Content Storage',
      'No Transaction Fees Ever',
      'Unlimited Spaces',
      'Up to 100,000 members',
    ],
  },
  {
    name: MEMBERUP_PLAN_ENUM.enterprise,
    prorated: true,
    promotional: false,
    displayName: 'Infinite',
    label: 'BEST FOR BRANDS',
    labelIcon: '',
    backgroundColor: '#7b51e0',
    backgroundHColor: '#7246db',
    boxShadow: 'none',
    containerBoxShadow: '2px 2px 28px rgba(0,0,0,0.5)',
    description: 'MemberUp’s most powerful plan, for growing businesses.',
    monthlyNormalPrice: 399,
    monthlyPrice: 299,
    monthlySave: 100,
    annualPrice: 3588,
    annualNormalMonthlyPrice: 399,
    annualMonthlyPrice: 299,
    annualSave: 1798,
    maxNumOfSpaces: MAX_NUMBER_OF_SPACES,
    maxNumOfCourses: MAX_NUMBER_OF_COURSES,
    maxNumOfMembers: 100000,
    options: [
      'Everything in In-Motion',
      'PLUS:',
      '1TB Content Storage',
      'No Transaction Fees Ever',
      'Unlimited Spaces',
      'Up to 100,000 members',
    ],
  },
]

export const USER_PLANS = [
  {
    name: USER_PLAN_ENUM.month,
    price: 39,
    recurringInterval: 'month',
    title: 'Price per month',
    planType: 'mo',
    description: 'All access to the community',
  },
  {
    name: USER_PLAN_ENUM.annual,
    price: 99,
    recurringInterval: 'year',
    title: 'Price per year',
    planType: 'yr',
    description: 'All access to the community',
  },
  {
    name: USER_PLAN_ENUM.lifetime,
    type: 'one_time',
    price: 100,
    planType: 'one_time',
    title: 'Lifetime price',
    description: 'All access to the community (lifetime)',
  },
]
