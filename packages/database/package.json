{"name": "@memberup/database", "version": "0.0.0", "type": "module", "scripts": {"db:status": "prisma migrate status", "db:generate": "prisma generate", "db:push": "prisma db push --skip-generate", "db:push:test": "dotenv -e ../../.env.test -- prisma db push --skip-generate"}, "dependencies": {"@planetscale/database": "^1.19.0", "@prisma/adapter-planetscale": "^5.22.0", "@prisma/client": "5.22.0", "prisma": "5.22.0"}, "devDependencies": {"dotenv-cli": "^7.4.2"}}